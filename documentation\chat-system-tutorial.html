<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Chat System Tutorial</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 30px;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
        }
        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        h3 {
            color: #2980b9;
            border-left: 3px solid #3498db;
            padding-left: 15px;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .example-box {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .example-box h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        .chat-box {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .chat-box h5 {
            color: white;
            margin-top: 0;
            font-weight: bold;
        }
        .chat-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Segoe UI', sans-serif;
        }
        .chat-message {
            background: white;
            border-radius: 15px;
            padding: 10px 15px;
            margin: 10px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            max-width: 70%;
        }
        .chat-message.sent {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .chat-message.received {
            background: #e9ecef;
            color: #333;
        }
        .chat-timestamp {
            font-size: 0.8em;
            opacity: 0.7;
            margin-top: 5px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .shortcode {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #34495e;
        }
        pre code {
            background: none;
            color: #ecf0f1;
            border: none;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💬 Chat System Tutorial</h1>
        <p style="text-align: center; font-size: 1.2em; color: #6c757d; margin-bottom: 30px;">
            Build powerful communication features with real-time chat and group messaging
        </p>

        <div class="section" id="chat-overview">
            <h2>💬 Chat System Overview</h2>
            <p>Database App Builder includes a comprehensive chat system that enables real-time communication between users. The chat system supports both one-on-one conversations and group chats, with features like file sharing, message history, and admin controls.</p>

            <div class="chat-box">
                <h5>🚀 Chat System Features:</h5>
                <ul>
                    <li><strong>Real-time Messaging:</strong> Instant message delivery and notifications</li>
                    <li><strong>Group Chat Management:</strong> Create and manage chat groups</li>
                    <li><strong>File Sharing:</strong> Share documents, images, and files</li>
                    <li><strong>Message History:</strong> Complete conversation archives</li>
                    <li><strong>User Management:</strong> Add/remove users from groups</li>
                    <li><strong>Admin Controls:</strong> Moderate conversations and manage permissions</li>
                    <li><strong>Mobile Responsive:</strong> Works perfectly on all devices</li>
                    <li><strong>Integration Ready:</strong> Embed chat anywhere on your site</li>
                </ul>
            </div>

            <div class="warning-box">
                <h5>⚠️ Prerequisites:</h5>
                <ul>
                    <li>Frontend user management must be enabled</li>
                    <li>Users need to be logged in to participate in chats</li>
                    <li>Proper user roles and permissions should be configured</li>
                    <li>WebSocket support recommended for real-time features</li>
                </ul>
            </div>
        </div>

        <div class="section" id="chat-groups">
            <h2>👥 Managing Chat Groups</h2>
            <p>Chat groups allow multiple users to communicate in organized conversations. Admins can create groups, add members, and control group settings.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Creating Chat Groups</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 💬 Chat Groups</strong></li>
                    <li>Click <strong>"Create New Group"</strong></li>
                    <li>Enter a descriptive group name</li>
                    <li>Add a group description (optional)</li>
                    <li>Select group privacy settings (public/private)</li>
                    <li>Add initial group members</li>
                    <li>Set group permissions and roles</li>
                    <li>Save the group configuration</li>
                </ol>

                <div class="example-box">
                    <h5>💬 Example: Project Team Chat Groups</h5>
                    <ul>
                        <li><strong>Development Team:</strong> Developers, QA, and project managers</li>
                        <li><strong>Marketing Team:</strong> Marketing staff and content creators</li>
                        <li><strong>Customer Support:</strong> Support agents and supervisors</li>
                        <li><strong>Executive Team:</strong> Leadership and department heads</li>
                        <li><strong>All Staff:</strong> Company-wide announcements and updates</li>
                        <li><strong>Project Alpha:</strong> Specific project team members</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Group Management Features</h4>
                <p>Comprehensive tools for managing chat groups:</p>

                <table>
                    <tr>
                        <th>Feature</th>
                        <th>Description</th>
                        <th>Admin Controls</th>
                    </tr>
                    <tr>
                        <td><strong>Member Management</strong></td>
                        <td>Add, remove, and manage group members</td>
                        <td>Bulk add/remove, role assignment</td>
                    </tr>
                    <tr>
                        <td><strong>Message Moderation</strong></td>
                        <td>Monitor and moderate group conversations</td>
                        <td>Delete messages, warn users, ban members</td>
                    </tr>
                    <tr>
                        <td><strong>Privacy Settings</strong></td>
                        <td>Control group visibility and access</td>
                        <td>Public, private, invite-only options</td>
                    </tr>
                    <tr>
                        <td><strong>Notification Settings</strong></td>
                        <td>Configure group notification preferences</td>
                        <td>Email notifications, push notifications</td>
                    </tr>
                    <tr>
                        <td><strong>File Sharing</strong></td>
                        <td>Enable/disable file sharing in groups</td>
                        <td>File type restrictions, size limits</td>
                    </tr>
                    <tr>
                        <td><strong>Archive Groups</strong></td>
                        <td>Archive inactive or completed project groups</td>
                        <td>Preserve history, disable new messages</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section" id="chat-interface">
            <h2>💻 Chat Interface and Features</h2>
            <p>The chat interface provides a modern, intuitive messaging experience with real-time updates and rich features.</p>

            <div class="chat-preview">
                <h5>💬 Chat Interface Preview:</h5>
                <div class="chat-message received">
                    <div>Welcome to the Development Team chat!</div>
                    <div class="chat-timestamp">Admin • 2 hours ago</div>
                </div>
                <div class="chat-message sent">
                    <div>Thanks! Excited to be part of the team.</div>
                    <div class="chat-timestamp">You • 1 hour ago</div>
                </div>
                <div class="chat-message received">
                    <div>Let's discuss the new project requirements.</div>
                    <div class="chat-timestamp">John Smith • 30 minutes ago</div>
                </div>
                <div class="chat-message received">
                    <div>📎 project-specs.pdf (2.3 MB)</div>
                    <div class="chat-timestamp">John Smith • 30 minutes ago</div>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Chat Interface Features</h4>
                <p>Rich messaging features for enhanced communication:</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>💬 Real-time Messaging</h4>
                        <ul>
                            <li>Instant message delivery</li>
                            <li>Typing indicators</li>
                            <li>Read receipts</li>
                            <li>Online status indicators</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>📎 File Sharing</h4>
                        <ul>
                            <li>Drag & drop file uploads</li>
                            <li>Image preview and gallery</li>
                            <li>Document sharing</li>
                            <li>File download tracking</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>🔍 Message Search</h4>
                        <ul>
                            <li>Search across all conversations</li>
                            <li>Filter by date, user, or file type</li>
                            <li>Highlight search results</li>
                            <li>Advanced search operators</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>📱 Mobile Responsive</h4>
                        <ul>
                            <li>Touch-optimized interface</li>
                            <li>Swipe gestures</li>
                            <li>Mobile notifications</li>
                            <li>Offline message sync</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" id="chat-embedding">
            <h2>🔗 Embedding Chat on Your Site</h2>
            <p>Integrate the chat system anywhere on your website using shortcodes and widgets.</p>

            <div class="step-by-step">
                <h4><span class="step-number">4</span>Chat Shortcodes</h4>
                <p>Use these shortcodes to embed chat functionality:</p>

                <div class="example-box">
                    <h5>📝 Basic Chat Shortcodes:</h5>

                    <p><strong>Full Chat Interface:</strong></p>
                    <p><span class="shortcode">[dab_chat]</span></p>

                    <p><strong>Specific Group Chat:</strong></p>
                    <p><span class="shortcode">[dab_chat group="development-team"]</span></p>

                    <p><strong>Chat Widget (Floating):</strong></p>
                    <p><span class="shortcode">[dab_chat_widget position="bottom-right"]</span></p>

                    <p><strong>Group List:</strong></p>
                    <p><span class="shortcode">[dab_chat_groups user_groups="true"]</span></p>

                    <p><strong>Private Message Interface:</strong></p>
                    <p><span class="shortcode">[dab_private_chat]</span></p>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">5</span>Advanced Chat Configuration</h4>
                <p>Customize chat behavior with advanced options:</p>

                <table>
                    <tr>
                        <th>Parameter</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><strong>height</strong></td>
                        <td>Set chat window height</td>
                        <td>height="400px"</td>
                    </tr>
                    <tr>
                        <td><strong>theme</strong></td>
                        <td>Choose chat theme</td>
                        <td>theme="dark" or theme="light"</td>
                    </tr>
                    <tr>
                        <td><strong>show_users</strong></td>
                        <td>Display online users list</td>
                        <td>show_users="true"</td>
                    </tr>
                    <tr>
                        <td><strong>allow_files</strong></td>
                        <td>Enable file sharing</td>
                        <td>allow_files="true"</td>
                    </tr>
                    <tr>
                        <td><strong>max_file_size</strong></td>
                        <td>Maximum file upload size</td>
                        <td>max_file_size="10MB"</td>
                    </tr>
                    <tr>
                        <td><strong>auto_scroll</strong></td>
                        <td>Auto-scroll to new messages</td>
                        <td>auto_scroll="true"</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section" id="chat-administration">
            <h2>⚙️ Chat Administration</h2>
            <p>Comprehensive admin tools for managing the chat system and monitoring conversations.</p>

            <div class="step-by-step">
                <h4><span class="step-number">6</span>Admin Dashboard Features</h4>
                <p>Monitor and manage all chat activities:</p>

                <div class="example-box">
                    <h5>📊 Admin Dashboard Includes:</h5>
                    <ul>
                        <li><strong>Message Statistics:</strong> Total messages, active users, popular groups</li>
                        <li><strong>User Activity:</strong> Most active users, login patterns, engagement metrics</li>
                        <li><strong>Group Analytics:</strong> Group participation, message volume, member growth</li>
                        <li><strong>File Sharing Stats:</strong> Upload volume, file types, storage usage</li>
                        <li><strong>Moderation Queue:</strong> Reported messages, flagged content, pending reviews</li>
                        <li><strong>System Health:</strong> Server status, connection quality, error logs</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">7</span>Moderation Tools</h4>
                <p>Keep conversations safe and productive:</p>

                <table>
                    <tr>
                        <th>Tool</th>
                        <th>Description</th>
                        <th>Use Cases</th>
                    </tr>
                    <tr>
                        <td><strong>Message Deletion</strong></td>
                        <td>Remove inappropriate messages</td>
                        <td>Spam, offensive content, violations</td>
                    </tr>
                    <tr>
                        <td><strong>User Warnings</strong></td>
                        <td>Send warnings to users</td>
                        <td>Policy violations, inappropriate behavior</td>
                    </tr>
                    <tr>
                        <td><strong>Temporary Mute</strong></td>
                        <td>Temporarily restrict user messaging</td>
                        <td>Cooling off periods, minor violations</td>
                    </tr>
                    <tr>
                        <td><strong>Group Removal</strong></td>
                        <td>Remove users from specific groups</td>
                        <td>Disruptive behavior, role changes</td>
                    </tr>
                    <tr>
                        <td><strong>Account Suspension</strong></td>
                        <td>Suspend user from all chat features</td>
                        <td>Serious violations, repeated offenses</td>
                    </tr>
                    <tr>
                        <td><strong>Content Filtering</strong></td>
                        <td>Automatic content moderation</td>
                        <td>Profanity filter, spam detection</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="tip-box">
            <h5>💡 Pro Tips: Chat System Best Practices</h5>
            <ul>
                <li><strong>Clear Guidelines:</strong> Establish and communicate chat usage policies</li>
                <li><strong>Group Organization:</strong> Create logical group structures for different purposes</li>
                <li><strong>Regular Monitoring:</strong> Check chat activity and user feedback regularly</li>
                <li><strong>Performance Optimization:</strong> Monitor server resources with active chat usage</li>
                <li><strong>User Training:</strong> Provide guidance on chat features and etiquette</li>
                <li><strong>Backup Strategy:</strong> Regularly backup important conversations and files</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: Chat Security</h5>
            <ul>
                <li><strong>User Authentication:</strong> Ensure only authenticated users can access chat</li>
                <li><strong>Data Privacy:</strong> Implement proper data protection measures</li>
                <li><strong>File Security:</strong> Scan uploaded files for malware and viruses</li>
                <li><strong>Message Encryption:</strong> Consider encryption for sensitive communications</li>
                <li><strong>Access Logs:</strong> Maintain logs of chat access and activities</li>
                <li><strong>Regular Updates:</strong> Keep chat system updated for security patches</li>
            </ul>
        </div>

        <div class="navigation-buttons">
            <a href="complete-user-guide.html" class="nav-button">← Back to Guide</a>
            <a href="user-management-tutorial.html" class="nav-button">Next: User Management →</a>
        </div>
    </div>
</body>
</html>
