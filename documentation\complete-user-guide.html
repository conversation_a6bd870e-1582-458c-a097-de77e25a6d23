<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Complete User Guide</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 30px;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
        }
        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        h3 {
            color: #2980b9;
            border-left: 3px solid #3498db;
            padding-left: 15px;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .example-box {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .example-box h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        code {
            background-color: #f8f8f8;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
            border: 1px solid #ddd;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #34495e;
        }
        pre code {
            background: none;
            color: #ecf0f1;
            border: none;
            padding: 0;
        }
        .shortcode {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .toc {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border: 1px solid #dee2e6;
        }
        .toc h2 {
            margin-top: 0;
            color: #495057;
            text-align: center;
        }
        .toc ul {
            list-style: none;
            padding: 0;
            columns: 2;
            column-gap: 30px;
        }
        .toc li {
            margin-bottom: 8px;
            break-inside: avoid;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
            font-weight: 500;
            display: block;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .toc a:hover {
            background: #3498db;
            color: white;
            transform: translateX(5px);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .screenshot-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            margin: 20px 0;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Database App Builder - Complete User Guide</h1>
        <p style="text-align: center; font-size: 1.2em; color: #6c757d; margin-bottom: 30px;">
            Master every feature and build powerful applications with confidence
        </p>

        <div class="toc">
            <h2>📚 Complete Tutorial Index</h2>
            <ul>
                <li><a href="#getting-started">🎯 Getting Started Guide</a></li>
                <li><a href="#app-templates">🏗️ App Templates & Quick Start</a></li>
                <li><a href="#tables-tutorial">🗃️ Tables & Database Management</a></li>
                <li><a href="#fields-tutorial">📝 Field Types & Configuration</a></li>
                <li><a href="#relationships-tutorial">🔗 Table Relationships</a></li>
                <li><a href="#forms-tutorial">📋 Form Builder Mastery</a></li>
                <li><a href="#views-tutorial">👁️ Views & Data Display</a></li>
                <li><a href="#dashboards-tutorial">📊 Dashboard Creation</a></li>
                <li><a href="#workflows-tutorial">⚡ Workflow Automation</a></li>
                <li><a href="#approvals-tutorial">✅ Approval Workflows</a></li>
                <li><a href="#data-management-tutorial">📊 Data Management</a></li>
                <li><a href="#user-management-tutorial">👥 User Management & Communication</a></li>
                <li><a href="#chat-system-tutorial">💬 Chat System</a></li>
                <li><a href="#analytics-tutorial">📈 Analytics & Intelligence</a></li>
                <li><a href="#woocommerce-tutorial">🛒 WooCommerce Integration</a></li>
                <li><a href="#payments-tutorial">💰 Payment Systems</a></li>
                <li><a href="#integrations-tutorial">🔌 Third-Party Integrations</a></li>
                <li><a href="#permissions-tutorial">🔐 Permissions & Security</a></li>
                <li><a href="#settings-tutorial">⚙️ Configuration & Settings</a></li>
                <li><a href="#advanced-tutorial">🎓 Advanced Features</a></li>
                <li><a href="#troubleshooting">🔧 Troubleshooting Guide</a></li>
            </ul>
        </div>

        <div class="section" id="getting-started">
            <h2>🎯 Getting Started Guide</h2>
            <p>Welcome to Database App Builder! This comprehensive guide will take you from zero to expert in building powerful database applications.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>First Login & Menu Overview</h4>
                <p>After activating the plugin, you'll see the "Database App Builder" menu in your WordPress admin. The menu is organized into logical groups:</p>

                <div class="example-box">
                    <h5>🎯 Menu Structure Overview:</h5>
                    <ul>
                        <li><strong>📊 Dashboard:</strong> Main overview and statistics</li>
                        <li><strong>🏗️ App Templates:</strong> Pre-built application templates for quick start</li>
                    </ul>

                    <h6>📋 Data Foundation:</h6>
                    <ul>
                        <li><strong>🗃️ Tables:</strong> Create and manage database tables</li>
                        <li><strong>📝 Fields:</strong> Configure field types and properties</li>
                        <li><strong>🔗 Relationships:</strong> Link tables together</li>
                    </ul>

                    <h6>🎨 Application Interface:</h6>
                    <ul>
                        <li><strong>📋 Forms:</strong> Build data collection forms</li>
                        <li><strong>👁️ Views:</strong> Create data display interfaces</li>
                        <li><strong>📈 Dashboards:</strong> Build interactive dashboards</li>
                    </ul>

                    <h6>⚙️ Workflow & Automation:</h6>
                    <ul>
                        <li><strong>⚙️ Workflow Builder:</strong> Create automated processes</li>
                        <li><strong>✅ Approval Workflows:</strong> Set up approval processes</li>
                        <li><strong>⏳ Pending Approvals:</strong> Manage approval tasks</li>
                    </ul>

                    <h6>📊 Data Management:</h6>
                    <ul>
                        <li><strong>📊 Data Management:</strong> Browse and manage all your data</li>
                    </ul>

                    <h6>👥 User Management & Communication:</h6>
                    <ul>
                        <li><strong>👥 Frontend Users:</strong> Manage frontend user accounts</li>
                        <li><strong>💬 Chat Groups:</strong> Create and manage chat groups</li>
                        <li><strong>🔐 Permissions Dashboard:</strong> Control access and permissions</li>
                    </ul>

                    <h6>📈 Analytics & Intelligence:</h6>
                    <ul>
                        <li><strong>📊 Report Builder:</strong> Create custom reports</li>
                        <li><strong>📈 Analytics Dashboard:</strong> View data insights</li>
                        <li><strong>🔍 Data Insights:</strong> AI-powered data analysis</li>
                        <li><strong>⏰ Scheduled Reports:</strong> Automate report generation</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Understanding the Application Building Process</h4>
                <p>Building applications follows a logical sequence. You can either start from scratch or use pre-built templates:</p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>1. Choose Template</strong><br>
                        <small>Or start from scratch</small>
                    </div>
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>2. Create Tables</strong><br>
                        <small>Define data structure</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>3. Add Fields</strong><br>
                        <small>Configure data types</small>
                    </div>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>4. Build Forms</strong><br>
                        <small>Create data entry</small>
                    </div>
                    <div style="background: #fce4ec; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>5. Create Views</strong><br>
                        <small>Display data</small>
                    </div>
                    <div style="background: #f1f8e9; padding: 15px; border-radius: 8px; text-align: center;">
                        <strong>6. Build Dashboards</strong><br>
                        <small>Visualize insights</small>
                    </div>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Navigation Tips</h4>
                <p>The menu groups are collapsible to keep your admin area organized:</p>

                <ul>
                    <li><strong>Click on group headers</strong> (like "Data Foundation") to expand/collapse sections</li>
                    <li><strong>Menu state is saved</strong> - your preferences persist between sessions</li>
                    <li><strong>Start with collapsed menus</strong> - only expand what you're currently working on</li>
                    <li><strong>Use the Dashboard</strong> for quick access to recent items and statistics</li>
                </ul>
            </div>

            <div class="tip-box">
                <h5>💡 Pro Tip: Start with App Templates</h5>
                <p>If you're new to the platform, start with <strong>🏗️ App Templates</strong> to get a complete application up and running quickly. You can then customize it to your needs. For learning purposes, try building a simple contact form from scratch to understand the workflow.</p>
            </div>
        </div>

        <div class="section" id="app-templates">
            <h2>🏗️ App Templates & Quick Start</h2>
            <p>App Templates provide pre-built applications that you can deploy with one click and customize to your needs. This is the fastest way to get started with Database App Builder.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Accessing App Templates</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 🏗️ App Templates</strong></li>
                    <li>Browse the available template categories</li>
                    <li>Preview template features and screenshots</li>
                    <li>Click <strong>"Install Template"</strong> for your chosen template</li>
                </ol>

                <div class="example-box">
                    <h5>📋 Available Template Categories:</h5>
                    <ul>
                        <li><strong>Business Management:</strong> CRM, Project Management, Inventory</li>
                        <li><strong>HR & Employee Management:</strong> Employee records, Leave management</li>
                        <li><strong>Customer Service:</strong> Support tickets, Knowledge base</li>
                        <li><strong>Event Management:</strong> Event registration, Attendee tracking</li>
                        <li><strong>Educational:</strong> Student management, Course tracking</li>
                        <li><strong>Healthcare:</strong> Patient records, Appointment scheduling</li>
                        <li><strong>Real Estate:</strong> Property listings, Client management</li>
                        <li><strong>E-commerce:</strong> Product catalog, Order management</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>One-Click Template Installation</h4>
                <p>Installing a template automatically creates all necessary components:</p>

                <div class="example-box">
                    <h5>🚀 What Gets Created Automatically:</h5>
                    <ul>
                        <li><strong>Database Tables:</strong> All required tables with proper structure</li>
                        <li><strong>Fields:</strong> Pre-configured fields with appropriate types</li>
                        <li><strong>Forms:</strong> Data entry forms with validation</li>
                        <li><strong>Views:</strong> Data display interfaces</li>
                        <li><strong>Dashboards:</strong> Analytics and overview dashboards</li>
                        <li><strong>Workflows:</strong> Basic automation processes</li>
                        <li><strong>Permissions:</strong> Role-based access controls</li>
                        <li><strong>Sample Data:</strong> Example records to get you started</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Template Customization</h4>
                <p>After installation, you can customize any aspect of the template:</p>

                <ol>
                    <li><strong>Modify Tables:</strong> Add/remove fields, change field types</li>
                    <li><strong>Customize Forms:</strong> Adjust layouts, add validation rules</li>
                    <li><strong>Update Views:</strong> Change display options, add filters</li>
                    <li><strong>Enhance Dashboards:</strong> Add widgets, modify charts</li>
                    <li><strong>Configure Workflows:</strong> Set up automation rules</li>
                    <li><strong>Adjust Permissions:</strong> Control who can access what</li>
                </ol>
            </div>

            <div class="example-box">
                <h5>📋 Example: CRM Template Walkthrough</h5>
                <p><strong>Template:</strong> Customer Relationship Management</p>
                <p><strong>Installation Time:</strong> 30 seconds</p>

                <p><strong>What You Get:</strong></p>
                <ul>
                    <li><strong>Contacts Table:</strong> Customer information, contact details</li>
                    <li><strong>Companies Table:</strong> Business information, relationships</li>
                    <li><strong>Deals Table:</strong> Sales pipeline, deal tracking</li>
                    <li><strong>Activities Table:</strong> Calls, meetings, emails</li>
                    <li><strong>Contact Form:</strong> Add new customers</li>
                    <li><strong>Deal Pipeline View:</strong> Visual sales pipeline</li>
                    <li><strong>Sales Dashboard:</strong> Revenue charts, conversion rates</li>
                    <li><strong>Follow-up Workflows:</strong> Automated reminders</li>
                </ul>

                <p><strong>Customization Options:</strong></p>
                <ul>
                    <li>Add custom fields (industry, source, etc.)</li>
                    <li>Modify deal stages to match your process</li>
                    <li>Add email automation workflows</li>
                    <li>Create custom reports and dashboards</li>
                </ul>
            </div>

            <div class="warning-box">
                <h5>⚠️ Important: Template Installation</h5>
                <p>Template installation creates new database tables and data. Make sure to:</p>
                <ul>
                    <li>Backup your database before installing templates</li>
                    <li>Review template features before installation</li>
                    <li>Understand that templates include sample data</li>
                    <li>Check for conflicts with existing table names</li>
                </ul>
            </div>

            <div class="tip-box">
                <h5>💡 Pro Tip: Template Learning</h5>
                <p>Even if you plan to build from scratch, install a template similar to your needs first. Study its structure, forms, and workflows to understand best practices, then build your custom solution with that knowledge.</p>
            </div>
        </div>

        <div class="section" id="tables-tutorial">
            <h2>🗃️ Tables & Database Management</h2>
            <p>Tables are the foundation of your applications. They store all your data and define the structure of your database. Navigate to <strong>Database App Builder → 🗃️ Tables</strong> to get started.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Creating Your First Table</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 🗃️ Tables</strong></li>
                    <li>Click <strong>"Add New Table"</strong> button</li>
                    <li>Enter a descriptive table name (e.g., "Customer Contacts")</li>
                    <li>Add an optional description explaining the table's purpose</li>
                    <li>Click <strong>"Create Table"</strong></li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Customer Management System</h5>
                    <p><strong>Table Name:</strong> Customer Contacts</p>
                    <p><strong>Description:</strong> Store customer information including contact details, preferences, and interaction history</p>
                    <p><strong>Generated Slug:</strong> customer_contacts (automatically created)</p>
                    <p><strong>Database Table:</strong> wp_dab_customer_contacts (with WordPress prefix)</p>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Table Management Features</h4>
                <p>Once created, you can manage your tables with these powerful features:</p>

                <table>
                    <tr>
                        <th>Action</th>
                        <th>Description</th>
                        <th>Use Case</th>
                        <th>Location</th>
                    </tr>
                    <tr>
                        <td><strong>Edit</strong></td>
                        <td>Modify table name and description</td>
                        <td>Update table information as needs change</td>
                        <td>Edit button in table list</td>
                    </tr>
                    <tr>
                        <td><strong>View Data</strong></td>
                        <td>Browse all records in the table</td>
                        <td>Quick data review and management</td>
                        <td>View Data button</td>
                    </tr>
                    <tr>
                        <td><strong>Manage Fields</strong></td>
                        <td>Add, edit, or remove table fields</td>
                        <td>Modify table structure</td>
                        <td>Fields button</td>
                    </tr>
                    <tr>
                        <td><strong>Export</strong></td>
                        <td>Download data in CSV, Excel, or PDF</td>
                        <td>Backup, reporting, or data migration</td>
                        <td>Export dropdown</td>
                    </tr>
                    <tr>
                        <td><strong>Duplicate</strong></td>
                        <td>Create a copy with same structure</td>
                        <td>Create similar tables quickly</td>
                        <td>Duplicate button</td>
                    </tr>
                    <tr>
                        <td><strong>Delete</strong></td>
                        <td>Permanently remove table and data</td>
                        <td>Clean up unused tables</td>
                        <td>Delete button (with confirmation)</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Table Data Management</h4>
                <p>Access your table data through <strong>Database App Builder → 📊 Data Management</strong>:</p>

                <ol>
                    <li><strong>Browse Records:</strong> View all data in a searchable, sortable table</li>
                    <li><strong>Add Records:</strong> Create new entries directly</li>
                    <li><strong>Edit Records:</strong> Modify existing data inline</li>
                    <li><strong>Delete Records:</strong> Remove individual records</li>
                    <li><strong>Bulk Actions:</strong> Perform operations on multiple records</li>
                    <li><strong>Advanced Search:</strong> Filter data with complex criteria</li>
                    <li><strong>Export Data:</strong> Download filtered results</li>
                </ol>

                <div class="example-box">
                    <h5>🔍 Data Management Features:</h5>
                    <ul>
                        <li><strong>Real-time Search:</strong> Find records instantly as you type</li>
                        <li><strong>Column Sorting:</strong> Click headers to sort by any field</li>
                        <li><strong>Pagination:</strong> Navigate through large datasets</li>
                        <li><strong>Record Count:</strong> See total records and filtered results</li>
                        <li><strong>Quick Actions:</strong> Edit, duplicate, or delete with one click</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">4</span>Planning Your Database Structure</h4>
                <p>Before creating multiple tables, plan your database structure carefully:</p>

                <div class="example-box">
                    <h5>🏢 Example: Complete Business System</h5>
                    <ul>
                        <li><strong>Customers:</strong> Customer information and contacts</li>
                        <li><strong>Products:</strong> Product catalog and inventory</li>
                        <li><strong>Orders:</strong> Sales transactions and order details</li>
                        <li><strong>Support Tickets:</strong> Customer service requests</li>
                        <li><strong>Employees:</strong> Staff information and roles</li>
                        <li><strong>Projects:</strong> Project management and tracking</li>
                        <li><strong>Invoices:</strong> Billing and payment tracking</li>
                        <li><strong>Activities:</strong> Log of all business activities</li>
                    </ul>
                </div>

                <div class="tip-box">
                    <h5>💡 Database Design Best Practices:</h5>
                    <ul>
                        <li><strong>Normalize Data:</strong> Avoid duplicate information across tables</li>
                        <li><strong>Use Relationships:</strong> Link related data instead of repeating it</li>
                        <li><strong>Plan for Growth:</strong> Consider future needs when designing structure</li>
                        <li><strong>Consistent Naming:</strong> Use clear, consistent naming conventions</li>
                        <li><strong>Document Purpose:</strong> Add descriptions to explain table purposes</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">5</span>Table Import and Export</h4>
                <p>Database App Builder supports importing and exporting data in multiple formats:</p>

                <div class="example-box">
                    <h5>📥 Import Options:</h5>
                    <ul>
                        <li><strong>CSV Import:</strong> Upload CSV files with automatic field mapping</li>
                        <li><strong>Excel Import:</strong> Import .xlsx files with multiple sheets</li>
                        <li><strong>JSON Import:</strong> Import structured JSON data</li>
                        <li><strong>WordPress Import:</strong> Import from other WordPress tables</li>
                    </ul>

                    <h5>📤 Export Options:</h5>
                    <ul>
                        <li><strong>CSV Export:</strong> Standard comma-separated values</li>
                        <li><strong>Excel Export:</strong> Formatted .xlsx files</li>
                        <li><strong>PDF Export:</strong> Formatted reports</li>
                        <li><strong>JSON Export:</strong> Structured data format</li>
                        <li><strong>SQL Export:</strong> Database backup format</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>⚠️ Important: Table Operations</h5>
                <ul>
                    <li><strong>Backup First:</strong> Always backup before major changes</li>
                    <li><strong>Test Imports:</strong> Try small datasets before large imports</li>
                    <li><strong>Check Relationships:</strong> Ensure related data integrity</li>
                    <li><strong>Deletion is Permanent:</strong> Table deletion cannot be undone</li>
                    <li><strong>Field Changes:</strong> Modifying field types may affect existing data</li>
                </ul>
            </div>
        </div>

        <div class="section" id="fields-tutorial">
            <h2>📝 Field Types & Configuration</h2>
            <p>Fields define what type of data each column in your tables can store. Database App Builder offers 30+ field types to handle any data requirement. Navigate to <strong>Database App Builder → 📝 Fields</strong> to manage fields.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Adding Fields to Tables</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 📝 Fields</strong></li>
                    <li>Select the table you want to add fields to</li>
                    <li>Click <strong>"Add New Field"</strong></li>
                    <li>Choose the appropriate field type</li>
                    <li>Configure field properties and validation</li>
                    <li>Click <strong>"Save Field"</strong></li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Customer Contact Fields</h5>
                    <ul>
                        <li><strong>First Name:</strong> Text field (required)</li>
                        <li><strong>Last Name:</strong> Text field (required)</li>
                        <li><strong>Email:</strong> Email field (required, unique)</li>
                        <li><strong>Phone:</strong> Text field with phone validation</li>
                        <li><strong>Company:</strong> Lookup field to Companies table</li>
                        <li><strong>Status:</strong> Select field (Lead, Customer, Inactive)</li>
                        <li><strong>Notes:</strong> Textarea field</li>
                        <li><strong>Created Date:</strong> Date field (auto-populated)</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Basic Field Types</h4>
                <p>Essential field types for most applications:</p>

                <table>
                    <tr>
                        <th>Field Type</th>
                        <th>Description</th>
                        <th>Use Cases</th>
                        <th>Validation Options</th>
                    </tr>
                    <tr>
                        <td><strong>Text</strong></td>
                        <td>Single line text input</td>
                        <td>Names, titles, short descriptions</td>
                        <td>Required, min/max length, pattern</td>
                    </tr>
                    <tr>
                        <td><strong>Textarea</strong></td>
                        <td>Multi-line text input</td>
                        <td>Descriptions, comments, notes</td>
                        <td>Required, character limits</td>
                    </tr>
                    <tr>
                        <td><strong>Number</strong></td>
                        <td>Numeric input</td>
                        <td>Quantities, prices, scores</td>
                        <td>Min/max values, decimal places</td>
                    </tr>
                    <tr>
                        <td><strong>Email</strong></td>
                        <td>Email address with validation</td>
                        <td>Contact information</td>
                        <td>Required, unique, format validation</td>
                    </tr>
                    <tr>
                        <td><strong>Date</strong></td>
                        <td>Date picker</td>
                        <td>Birthdays, deadlines, events</td>
                        <td>Date range, required</td>
                    </tr>
                    <tr>
                        <td><strong>Select</strong></td>
                        <td>Dropdown selection</td>
                        <td>Status, category, priority</td>
                        <td>Required, custom options</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Advanced Field Types</h4>
                <p>Powerful field types for complex applications:</p>

                <table>
                    <tr>
                        <th>Field Type</th>
                        <th>Description</th>
                        <th>Use Cases</th>
                        <th>Special Features</th>
                    </tr>
                    <tr>
                        <td><strong>Lookup</strong></td>
                        <td>Reference to another table</td>
                        <td>Customer in Orders, Category in Products</td>
                        <td>Auto-complete, relationship creation</td>
                    </tr>
                    <tr>
                        <td><strong>Formula</strong></td>
                        <td>Calculated field</td>
                        <td>Total price, age calculation</td>
                        <td>Mathematical expressions, functions</td>
                    </tr>
                    <tr>
                        <td><strong>Auto Increment</strong></td>
                        <td>Automatically incrementing number</td>
                        <td>Invoice numbers, ticket IDs</td>
                        <td>Custom prefix, starting number</td>
                    </tr>
                    <tr>
                        <td><strong>File Upload</strong></td>
                        <td>File attachment</td>
                        <td>Documents, images, attachments</td>
                        <td>File type restrictions, size limits</td>
                    </tr>
                    <tr>
                        <td><strong>Signature</strong></td>
                        <td>Digital signature capture</td>
                        <td>Contracts, approvals</td>
                        <td>Touch/mouse drawing, save as image</td>
                    </tr>
                    <tr>
                        <td><strong>Currency</strong></td>
                        <td>Money values with formatting</td>
                        <td>Prices, salaries, budgets</td>
                        <td>Currency symbols, decimal precision</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">4</span>Modern UI Field Types</h4>
                <p>Advanced interface components for rich user experiences:</p>

                <table>
                    <tr>
                        <th>Field Type</th>
                        <th>Description</th>
                        <th>Use Cases</th>
                        <th>Features</th>
                    </tr>
                    <tr>
                        <td><strong>Kanban Board</strong></td>
                        <td>Visual task management</td>
                        <td>Project management, workflows</td>
                        <td>Drag & drop, custom columns</td>
                    </tr>
                    <tr>
                        <td><strong>Calendar View</strong></td>
                        <td>Calendar interface</td>
                        <td>Events, appointments, schedules</td>
                        <td>Month/week/day views, event creation</td>
                    </tr>
                    <tr>
                        <td><strong>Timeline View</strong></td>
                        <td>Chronological display</td>
                        <td>Project timelines, history</td>
                        <td>Interactive timeline, milestones</td>
                    </tr>
                    <tr>
                        <td><strong>Progress Tracker</strong></td>
                        <td>Progress visualization</td>
                        <td>Project completion, goals</td>
                        <td>Progress bars, percentage display</td>
                    </tr>
                    <tr>
                        <td><strong>Inline Table</strong></td>
                        <td>Embedded table within form</td>
                        <td>Order items, task lists</td>
                        <td>Add/remove rows, calculations</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">5</span>Field Configuration Options</h4>
                <p>Every field can be customized with these options:</p>

                <div class="example-box">
                    <h5>🔧 General Settings:</h5>
                    <ul>
                        <li><strong>Field Label:</strong> Display name for the field</li>
                        <li><strong>Field Name:</strong> Database column name (auto-generated)</li>
                        <li><strong>Description:</strong> Help text for users</li>
                        <li><strong>Required:</strong> Make field mandatory</li>
                        <li><strong>Default Value:</strong> Pre-populate with default</li>
                        <li><strong>Placeholder:</strong> Hint text in empty fields</li>
                    </ul>

                    <h5>🔒 Validation Rules:</h5>
                    <ul>
                        <li><strong>Minimum/Maximum Length:</strong> Text length limits</li>
                        <li><strong>Minimum/Maximum Value:</strong> Numeric range limits</li>
                        <li><strong>Pattern Validation:</strong> Regular expression matching</li>
                        <li><strong>Unique Values:</strong> Prevent duplicate entries</li>
                        <li><strong>Custom Validation:</strong> JavaScript validation functions</li>
                    </ul>

                    <h5>🎨 Display Options:</h5>
                    <ul>
                        <li><strong>Field Width:</strong> Control field size in forms</li>
                        <li><strong>CSS Classes:</strong> Custom styling</li>
                        <li><strong>Conditional Display:</strong> Show/hide based on other fields</li>
                        <li><strong>Read-only:</strong> Display-only fields</li>
                        <li><strong>Hidden:</strong> Store data without displaying</li>
                    </ul>
                </div>
            </div>

            <div class="tip-box">
                <h5>💡 Pro Tips: Field Design</h5>
                <ul>
                    <li><strong>Plan Field Types:</strong> Choose the most appropriate type for your data</li>
                    <li><strong>Use Validation:</strong> Prevent bad data entry with proper validation</li>
                    <li><strong>Consistent Naming:</strong> Use clear, consistent field names</li>
                    <li><strong>Group Related Fields:</strong> Organize fields logically in forms</li>
                    <li><strong>Test Thoroughly:</strong> Test all field types and validations</li>
                </ul>
            </div>
        </div>

        <div class="section" id="relationships-tutorial">
            <h2>🔗 Table Relationships</h2>
            <p>Relationships connect tables together, allowing you to link related data and avoid duplication. Navigate to <strong>Database App Builder → 🔗 Relationships</strong> to manage table connections.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Understanding Relationship Types</h4>
                <p>Database App Builder supports three types of relationships:</p>

                <div class="example-box">
                    <h5>🔗 Relationship Types:</h5>
                    <ul>
                        <li><strong>One-to-Many:</strong> One record relates to multiple records (Customer → Orders)</li>
                        <li><strong>Many-to-One:</strong> Multiple records relate to one record (Orders → Customer)</li>
                        <li><strong>Many-to-Many:</strong> Multiple records relate to multiple records (Students ↔ Courses)</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Creating Relationships</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 🔗 Relationships</strong></li>
                    <li>Click <strong>"Add New Relationship"</strong></li>
                    <li>Select the <strong>Parent Table</strong> (the "one" side)</li>
                    <li>Select the <strong>Child Table</strong> (the "many" side)</li>
                    <li>Choose the <strong>relationship type</strong></li>
                    <li>Configure <strong>display options</strong></li>
                    <li>Click <strong>"Create Relationship"</strong></li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Customer-Orders Relationship</h5>
                    <p><strong>Relationship Type:</strong> One-to-Many</p>
                    <p><strong>Parent Table:</strong> Customers (one customer)</p>
                    <p><strong>Child Table:</strong> Orders (many orders)</p>
                    <p><strong>Result:</strong> Each customer can have multiple orders, but each order belongs to one customer</p>

                    <p><strong>What This Creates:</strong></p>
                    <ul>
                        <li>Customer lookup field in Orders table</li>
                        <li>Orders list in Customer view</li>
                        <li>Automatic data linking</li>
                        <li>Referential integrity</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Relationship Benefits</h4>
                <p>Properly designed relationships provide numerous advantages:</p>

                <div class="example-box">
                    <h5>✅ Benefits of Relationships:</h5>
                    <ul>
                        <li><strong>Data Integrity:</strong> Prevents orphaned records</li>
                        <li><strong>Reduced Duplication:</strong> Store data once, reference everywhere</li>
                        <li><strong>Automatic Updates:</strong> Changes propagate to related records</li>
                        <li><strong>Better Reporting:</strong> Join data across tables easily</li>
                        <li><strong>Improved Performance:</strong> Normalized data structure</li>
                        <li><strong>Easier Maintenance:</strong> Update in one place</li>
                    </ul>
                </div>
            </div>

            <div class="warning-box">
                <h5>⚠️ Important: Relationship Planning</h5>
                <ul>
                    <li><strong>Plan First:</strong> Design relationships before adding data</li>
                    <li><strong>Avoid Circular References:</strong> Don't create loops between tables</li>
                    <li><strong>Consider Performance:</strong> Too many relationships can slow queries</li>
                    <li><strong>Test Thoroughly:</strong> Verify relationships work as expected</li>
                    <li><strong>Document Relationships:</strong> Keep track of table connections</li>
                </ul>
            </div>
        </div>

        <div class="section" id="quick-links">
            <h2>📚 Detailed Tutorial Links</h2>
            <p>Access comprehensive step-by-step tutorials for each major feature:</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📋 Forms Builder</h4>
                    <p>Master form creation with advanced validation, multi-step forms, and automation.</p>
                    <a href="forms-tutorial.html" class="nav-button">View Tutorial →</a>
                </div>
                <div class="feature-card">
                    <h4>📈 Analytics & Intelligence</h4>
                    <p>Create powerful reports, dashboards, and gain data insights with AI-powered analytics.</p>
                    <a href="analytics-tutorial.html" class="nav-button">View Tutorial →</a>
                </div>
                <div class="feature-card">
                    <h4>⚡ Workflow Automation</h4>
                    <p>Automate business processes with visual workflow builder and approval systems.</p>
                    <a href="workflow-automation-tutorial.html" class="nav-button">View Tutorial →</a>
                </div>
                <div class="feature-card">
                    <h4>🛒 WooCommerce Integration</h4>
                    <p>Enhance your e-commerce store with custom fields, advanced reporting, and automation.</p>
                    <a href="woocommerce-tutorial.html" class="nav-button">View Tutorial →</a>
                </div>
                <div class="feature-card">
                    <h4>💬 Chat System</h4>
                    <p>Build real-time communication features with group chat and file sharing.</p>
                    <a href="chat-system-tutorial.html" class="nav-button">View Tutorial →</a>
                </div>
                <div class="feature-card">
                    <h4>👥 User Management</h4>
                    <p>Manage frontend users, permissions, and access controls effectively.</p>
                    <a href="user-management-tutorial.html" class="nav-button">View Tutorial →</a>
                </div>
            </div>
        </div>

        <div class="section" id="getting-help">
            <h2>🆘 Getting Help and Support</h2>
            <p>If you need additional assistance beyond this documentation:</p>

            <div class="example-box">
                <h5>📞 Support Channels:</h5>
                <ul>
                    <li><strong>Documentation:</strong> Check all tutorial files for detailed guides</li>
                    <li><strong>Video Tutorials:</strong> Watch step-by-step video guides</li>
                    <li><strong>Community Forum:</strong> Connect with other users and developers</li>
                    <li><strong>Support Tickets:</strong> Submit technical support requests</li>
                    <li><strong>Live Chat:</strong> Get real-time assistance during business hours</li>
                    <li><strong>Knowledge Base:</strong> Search frequently asked questions</li>
                </ul>
            </div>

            <div class="tip-box">
                <h5>💡 Before Contacting Support:</h5>
                <ul>
                    <li><strong>Check Documentation:</strong> Review relevant tutorial sections</li>
                    <li><strong>Search Knowledge Base:</strong> Look for similar issues and solutions</li>
                    <li><strong>Test in Staging:</strong> Try to reproduce issues in a test environment</li>
                    <li><strong>Gather Information:</strong> Note error messages, steps to reproduce, and system details</li>
                    <li><strong>Check System Requirements:</strong> Ensure your setup meets all requirements</li>
                </ul>
            </div>
        </div>

        <div class="section" id="whats-next">
            <h2>🚀 What's Next?</h2>
            <p>Continue your Database App Builder journey:</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>🎯 Start Building</strong><br>
                    <small>Create your first application using templates or from scratch</small>
                </div>
                <div style="background: #f3e5f5; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>📊 Add Analytics</strong><br>
                    <small>Build dashboards and reports to gain insights from your data</small>
                </div>
                <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>⚡ Automate Workflows</strong><br>
                    <small>Set up automation to streamline your business processes</small>
                </div>
                <div style="background: #fff3e0; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>🔗 Integrate Systems</strong><br>
                    <small>Connect with third-party services and payment systems</small>
                </div>
                <div style="background: #fce4ec; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>👥 Manage Users</strong><br>
                    <small>Set up user management and communication features</small>
                </div>
                <div style="background: #f1f8e9; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>📈 Scale Up</strong><br>
                    <small>Optimize performance and add advanced features</small>
                </div>
            </div>
        </div>

        <div class="navigation-buttons">
            <a href="forms-tutorial.html" class="nav-button">Start with Forms →</a>
            <a href="analytics-tutorial.html" class="nav-button">Explore Analytics →</a>
        </div>
    </div>
</body>
</html>
