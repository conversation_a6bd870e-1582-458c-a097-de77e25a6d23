<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Forms Tutorial</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 30px;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
        }
        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        h3 {
            color: #2980b9;
            border-left: 3px solid #3498db;
            padding-left: 15px;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .example-box {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .example-box h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        code {
            background-color: #f8f8f8;
            padding: 3px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
            border: 1px solid #ddd;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #34495e;
        }
        pre code {
            background: none;
            color: #ecf0f1;
            border: none;
            padding: 0;
        }
        .shortcode {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Forms Builder Mastery</h1>
        <p style="text-align: center; font-size: 1.2em; color: #6c757d; margin-bottom: 30px;">
            Create powerful data collection forms with advanced features and validation
        </p>

        <div class="section" id="forms-overview">
            <h2>📋 Forms Overview</h2>
            <p>Forms are the primary way users interact with your database applications. Database App Builder provides a powerful form builder that creates responsive, validated forms automatically from your table structure. Navigate to <strong>Database App Builder → 📋 Forms</strong> to get started.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Creating Your First Form</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 📋 Forms</strong></li>
                    <li>Click <strong>"Add New Form"</strong></li>
                    <li>Enter a descriptive form name</li>
                    <li>Select the target table for data storage</li>
                    <li>Choose which fields to include</li>
                    <li>Configure form layout and validation</li>
                    <li>Click <strong>"Save Form"</strong></li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Customer Registration Form</h5>
                    <p><strong>Form Name:</strong> Customer Registration</p>
                    <p><strong>Target Table:</strong> Customers</p>
                    <p><strong>Purpose:</strong> Collect new customer information</p>

                    <p><strong>Included Fields:</strong></p>
                    <ul>
                        <li>First Name (required)</li>
                        <li>Last Name (required)</li>
                        <li>Email (required, unique validation)</li>
                        <li>Phone Number</li>
                        <li>Company (lookup to Companies table)</li>
                        <li>Industry (dropdown selection)</li>
                        <li>Notes (optional textarea)</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Form Configuration Options</h4>
                <p>Customize your forms with these powerful options:</p>

                <table>
                    <tr>
                        <th>Setting</th>
                        <th>Description</th>
                        <th>Use Cases</th>
                    </tr>
                    <tr>
                        <td><strong>Form Title</strong></td>
                        <td>Display title shown to users</td>
                        <td>Clear identification of form purpose</td>
                    </tr>
                    <tr>
                        <td><strong>Description</strong></td>
                        <td>Instructions or help text</td>
                        <td>Guide users on how to complete the form</td>
                    </tr>
                    <tr>
                        <td><strong>Submit Button Text</strong></td>
                        <td>Custom text for submit button</td>
                        <td>"Register Now", "Submit Application", etc.</td>
                    </tr>
                    <tr>
                        <td><strong>Success Message</strong></td>
                        <td>Message shown after successful submission</td>
                        <td>Confirmation and next steps</td>
                    </tr>
                    <tr>
                        <td><strong>Redirect URL</strong></td>
                        <td>Page to redirect after submission</td>
                        <td>Thank you page, payment page, etc.</td>
                    </tr>
                    <tr>
                        <td><strong>Email Notifications</strong></td>
                        <td>Send emails on form submission</td>
                        <td>Notify admins, send confirmations</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Field Layout and Organization</h4>
                <p>Organize your form fields for optimal user experience:</p>

                <div class="example-box">
                    <h5>🎨 Layout Options:</h5>
                    <ul>
                        <li><strong>Single Column:</strong> Traditional vertical layout</li>
                        <li><strong>Two Column:</strong> Side-by-side fields for compact forms</li>
                        <li><strong>Three Column:</strong> Maximum density for wide screens</li>
                        <li><strong>Custom Grid:</strong> Mix of column widths for optimal design</li>
                        <li><strong>Tabbed Sections:</strong> Group related fields in tabs</li>
                        <li><strong>Accordion Sections:</strong> Collapsible field groups</li>
                    </ul>

                    <h5>📱 Responsive Design:</h5>
                    <ul>
                        <li><strong>Mobile Optimization:</strong> Automatically adapts to small screens</li>
                        <li><strong>Touch-Friendly:</strong> Large buttons and inputs for mobile</li>
                        <li><strong>Progressive Enhancement:</strong> Works on all devices</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="form-validation">
            <h2>🔒 Form Validation & Security</h2>
            <p>Ensure data quality and security with comprehensive validation options:</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Field-Level Validation</h4>
                <p>Configure validation rules for each field:</p>

                <table>
                    <tr>
                        <th>Validation Type</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><strong>Required Fields</strong></td>
                        <td>Must be filled before submission</td>
                        <td>Name, Email, Phone</td>
                    </tr>
                    <tr>
                        <td><strong>Format Validation</strong></td>
                        <td>Must match specific pattern</td>
                        <td>Email format, phone format</td>
                    </tr>
                    <tr>
                        <td><strong>Length Limits</strong></td>
                        <td>Minimum/maximum character count</td>
                        <td>Password 8-20 characters</td>
                    </tr>
                    <tr>
                        <td><strong>Numeric Ranges</strong></td>
                        <td>Number must be within range</td>
                        <td>Age 18-100, Quantity 1-999</td>
                    </tr>
                    <tr>
                        <td><strong>Unique Values</strong></td>
                        <td>Prevent duplicate entries</td>
                        <td>Email addresses, usernames</td>
                    </tr>
                    <tr>
                        <td><strong>Custom Validation</strong></td>
                        <td>JavaScript validation functions</td>
                        <td>Complex business rules</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Conditional Logic</h4>
                <p>Show/hide fields based on user selections:</p>

                <div class="example-box">
                    <h5>🔄 Conditional Logic Examples:</h5>
                    <ul>
                        <li><strong>Show Company Field:</strong> Only when "Business" customer type is selected</li>
                        <li><strong>Require Tax ID:</strong> Only for business customers</li>
                        <li><strong>Show Shipping Address:</strong> Only when "Ship to different address" is checked</li>
                        <li><strong>Display Payment Fields:</strong> Based on selected payment method</li>
                        <li><strong>Show Additional Questions:</strong> Based on previous answers</li>
                    </ul>

                    <h5>⚙️ Logic Configuration:</h5>
                    <ol>
                        <li>Select the field to control</li>
                        <li>Choose the trigger field</li>
                        <li>Set the condition (equals, contains, greater than, etc.)</li>
                        <li>Define the action (show, hide, require, disable)</li>
                        <li>Test the logic thoroughly</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="section" id="form-features">
            <h2>🚀 Advanced Form Features</h2>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Multi-Step Forms</h4>
                <p>Break long forms into manageable steps:</p>

                <div class="example-box">
                    <h5>📋 Multi-Step Form Benefits:</h5>
                    <ul>
                        <li><strong>Improved Completion Rates:</strong> Less overwhelming for users</li>
                        <li><strong>Better User Experience:</strong> Logical progression through sections</li>
                        <li><strong>Progress Tracking:</strong> Users see how much is left</li>
                        <li><strong>Conditional Branching:</strong> Skip irrelevant sections</li>
                        <li><strong>Save Progress:</strong> Allow users to return later</li>
                    </ul>

                    <h5>🔧 Configuration Steps:</h5>
                    <ol>
                        <li>Enable multi-step mode in form settings</li>
                        <li>Group related fields into logical steps</li>
                        <li>Configure step titles and descriptions</li>
                        <li>Set up navigation buttons (Next, Previous, Save)</li>
                        <li>Add progress indicator</li>
                        <li>Test the complete flow</li>
                    </ol>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>File Uploads and Media</h4>
                <p>Handle file attachments and media uploads:</p>

                <table>
                    <tr>
                        <th>Upload Type</th>
                        <th>Supported Formats</th>
                        <th>Use Cases</th>
                    </tr>
                    <tr>
                        <td><strong>Documents</strong></td>
                        <td>PDF, DOC, DOCX, TXT</td>
                        <td>Resumes, contracts, reports</td>
                    </tr>
                    <tr>
                        <td><strong>Images</strong></td>
                        <td>JPG, PNG, GIF, WebP</td>
                        <td>Profile photos, product images</td>
                    </tr>
                    <tr>
                        <td><strong>Audio</strong></td>
                        <td>MP3, WAV, OGG</td>
                        <td>Voice recordings, music</td>
                    </tr>
                    <tr>
                        <td><strong>Video</strong></td>
                        <td>MP4, WebM, AVI</td>
                        <td>Testimonials, presentations</td>
                    </tr>
                    <tr>
                        <td><strong>Archives</strong></td>
                        <td>ZIP, RAR, 7Z</td>
                        <td>Multiple file submissions</td>
                    </tr>
                </table>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">3</span>Form Automation</h4>
                <p>Automate actions after form submission:</p>

                <div class="example-box">
                    <h5>⚡ Automation Options:</h5>
                    <ul>
                        <li><strong>Email Notifications:</strong> Send to admins, users, or custom lists</li>
                        <li><strong>Workflow Triggers:</strong> Start approval processes or other workflows</li>
                        <li><strong>Data Processing:</strong> Calculate values, update related records</li>
                        <li><strong>Third-Party Integration:</strong> Send data to external systems</li>
                        <li><strong>Payment Processing:</strong> Integrate with payment gateways</li>
                        <li><strong>User Account Creation:</strong> Automatically create user accounts</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="form-display">
            <h2>🎨 Form Display and Integration</h2>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Embedding Forms</h4>
                <p>Display forms on your website using shortcodes:</p>

                <div class="example-box">
                    <h5>📝 Shortcode Usage:</h5>
                    <p>Basic form display:</p>
                    <pre><code>[dab_form id="1"]</code></pre>

                    <p>Form with custom styling:</p>
                    <pre><code>[dab_form id="1" class="custom-form" style="max-width: 600px;"]</code></pre>

                    <p>Form with redirect:</p>
                    <pre><code>[dab_form id="1" redirect="/thank-you/"]</code></pre>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Form Styling and Themes</h4>
                <p>Customize the appearance to match your brand:</p>

                <div class="example-box">
                    <h5>🎨 Styling Options:</h5>
                    <ul>
                        <li><strong>Pre-built Themes:</strong> Professional, Modern, Minimal, Classic</li>
                        <li><strong>Color Schemes:</strong> Match your brand colors</li>
                        <li><strong>Typography:</strong> Custom fonts and sizes</li>
                        <li><strong>Layout Options:</strong> Spacing, borders, shadows</li>
                        <li><strong>Custom CSS:</strong> Complete control over appearance</li>
                        <li><strong>Mobile Responsive:</strong> Automatic mobile optimization</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tip-box">
            <h5>💡 Pro Tips: Form Best Practices</h5>
            <ul>
                <li><strong>Keep It Simple:</strong> Only ask for essential information</li>
                <li><strong>Clear Labels:</strong> Use descriptive field labels and help text</li>
                <li><strong>Logical Flow:</strong> Organize fields in a logical sequence</li>
                <li><strong>Test Thoroughly:</strong> Test on different devices and browsers</li>
                <li><strong>Monitor Performance:</strong> Track completion rates and optimize</li>
                <li><strong>Provide Feedback:</strong> Clear error messages and success confirmations</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: Form Security</h5>
            <ul>
                <li><strong>Validate Server-Side:</strong> Never trust client-side validation alone</li>
                <li><strong>Sanitize Input:</strong> Clean all user input before storing</li>
                <li><strong>Use CSRF Protection:</strong> Prevent cross-site request forgery</li>
                <li><strong>Rate Limiting:</strong> Prevent spam and abuse</li>
                <li><strong>Secure File Uploads:</strong> Validate file types and scan for malware</li>
            </ul>
        </div>

        <div class="navigation-buttons">
            <a href="complete-user-guide.html" class="nav-button">← Back to Guide</a>
            <a href="views-tutorial.html" class="nav-button">Next: Views Tutorial →</a>
        </div>
    </div>
</body>
</html>
