<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics & Reporting Tutorial - Database App Builder</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
            text-align: center;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        .phase-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
            margin-left: 10px;
        }
        .analytics-overview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
        }
        .feature-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 1.3em;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 1.2em;
        }
        .dashboard-preview {
            background: #2c3e50;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            color: white;
        }
        .dashboard-widget {
            background: #34495e;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #3498db;
        }
        .widget-title {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        .widget-value {
            font-size: 2em;
            font-weight: bold;
            color: #ecf0f1;
        }
        .widget-change {
            font-size: 0.9em;
            color: #27ae60;
        }
        .report-example {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
        }
        .report-example h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
            font-size: 1.3em;
        }
        .chart-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .chart-type {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .chart-type h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .ai-insights {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border: 2px solid #27ae60;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
        }
        .ai-insights h5 {
            color: #155724;
            margin-top: 0;
            font-weight: bold;
            font-size: 1.3em;
        }
        .insight-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #27ae60;
        }
        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .warning-box h5 {
            color: #721c24;
            margin-top: 0;
            font-weight: bold;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 Analytics & Reporting Tutorial <span class="phase-badge">Phase 3</span></h1>

        <div class="analytics-overview">
            <h3>🚀 Transform Data into Actionable Insights</h3>
            <p style="font-size: 1.2em; color: #6c757d;">
                Phase 3 brings enterprise-level analytics capabilities that rival Tableau, Power BI, and Looker
            </p>
        </div>

        <h2>🎯 Analytics System Overview</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <h4><div class="feature-icon">📊</div>Advanced Report Builder</h4>
                <p>Visual drag-and-drop report designer with advanced filtering and multiple visualization types.</p>
                <ul>
                    <li>Drag-and-drop interface</li>
                    <li>Multi-data source support</li>
                    <li>Advanced filtering & aggregation</li>
                    <li>Export to PDF, Excel, CSV</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4><div class="feature-icon">🤖</div>AI-Powered Insights</h4>
                <p>Automated pattern detection, trend analysis, and smart recommendations powered by AI.</p>
                <ul>
                    <li>Automatic trend detection</li>
                    <li>Anomaly identification</li>
                    <li>Pattern recognition</li>
                    <li>Predictive analytics</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4><div class="feature-icon">📱</div>Real-time Dashboards</h4>
                <p>Live data streaming with interactive widgets and mobile-responsive design.</p>
                <ul>
                    <li>Real-time data updates</li>
                    <li>Interactive widgets</li>
                    <li>Mobile optimization</li>
                    <li>Public sharing options</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4><div class="feature-icon">⏰</div>Automated Reporting</h4>
                <p>Schedule reports for automatic generation and email distribution.</p>
                <ul>
                    <li>Flexible scheduling</li>
                    <li>Email distribution</li>
                    <li>Multiple export formats</li>
                    <li>Failure notifications</li>
                </ul>
            </div>
        </div>

        <h2>📊 Creating Your First Report</h2>

        <div class="step-by-step">
            <h4><span class="step-number">1</span>Report Builder Basics</h4>
            <ol>
                <li>Navigate to <strong>Database App Builder → 📊 Report Builder</strong></li>
                <li>Click <strong>"Create New Report"</strong></li>
                <li>Enter a descriptive report name and description</li>
                <li>Select your primary data source (database table)</li>
                <li>Choose fields to include in your report</li>
                <li>Configure joins if using multiple tables</li>
                <li>Apply filters and sorting criteria</li>
                <li>Select visualization type (chart, table, etc.)</li>
                <li>Configure styling and formatting options</li>
                <li>Preview your report</li>
                <li>Save and publish your report</li>
            </ol>
        </div>

        <div class="step-by-step">
            <h4><span class="step-number">2</span>Advanced Report Configuration</h4>
            <p>Enhance your reports with powerful configuration options:</p>

            <table>
                <tr>
                    <th>Feature</th>
                    <th>Description</th>
                    <th>Use Cases</th>
                </tr>
                <tr>
                    <td><strong>Data Sources</strong></td>
                    <td>Select from custom tables or WordPress core tables</td>
                    <td>Customer data, order history, user analytics</td>
                </tr>
                <tr>
                    <td><strong>Field Selection</strong></td>
                    <td>Choose specific fields from your data source</td>
                    <td>Name, email, order total, date created</td>
                </tr>
                <tr>
                    <td><strong>Calculated Fields</strong></td>
                    <td>Create custom calculations and formulas</td>
                    <td>Profit margins, age calculations, totals</td>
                </tr>
                <tr>
                    <td><strong>Grouping</strong></td>
                    <td>Group data by specific fields</td>
                    <td>Sales by month, customers by region</td>
                </tr>
                <tr>
                    <td><strong>Aggregations</strong></td>
                    <td>Sum, count, average, min, max functions</td>
                    <td>Total sales, average order value</td>
                </tr>
                <tr>
                    <td><strong>Filters</strong></td>
                    <td>Limit data based on conditions</td>
                    <td>Date ranges, status values, categories</td>
                </tr>
            </table>
        </div>

        <div class="report-example">
            <h5>📋 Example: Sales Performance Report</h5>

            <div class="code-example">
Report Name: Monthly Sales Performance
Data Source: sales_orders
Fields Selected:
- order_date (grouped by month)
- total_amount (sum)
- customer_name (count distinct)
- product_category (group by)

Filters:
- order_date >= last 12 months
- status = "completed"

Visualization: Bar Chart
Grouping: By month
Aggregation: SUM(total_amount)
</div>
        </div>

        <h2>📈 Chart Types & Visualizations</h2>

        <div class="chart-types">
            <div class="chart-type">
                <h5>📊 Bar Charts</h5>
                <p>Perfect for comparing categories and showing trends over time</p>
            </div>

            <div class="chart-type">
                <h5>📈 Line Charts</h5>
                <p>Ideal for showing trends and changes over time periods</p>
            </div>

            <div class="chart-type">
                <h5>🥧 Pie Charts</h5>
                <p>Great for showing proportions and percentage breakdowns</p>
            </div>

            <div class="chart-type">
                <h5>📋 Data Tables</h5>
                <p>Detailed data display with sorting and filtering capabilities</p>
            </div>

            <div class="chart-type">
                <h5>🎯 Gauge Charts</h5>
                <p>Perfect for KPIs and performance indicators</p>
            </div>

            <div class="chart-type">
                <h5>🗂️ Pivot Tables</h5>
                <p>Advanced data analysis with cross-tabulation</p>
            </div>
        </div>

        <h2>📱 Building Interactive Dashboards</h2>

        <div class="step-by-step">
            <h4><span class="step-number">2</span>Dashboard Creation Process</h4>
            <ol>
                <li>Navigate to <strong>Analytics Dashboard</strong></li>
                <li>Click <strong>"Create Dashboard"</strong></li>
                <li>Design your layout using drag-and-drop</li>
                <li>Add widgets (metrics, charts, tables)</li>
                <li>Configure real-time refresh settings</li>
                <li>Set up filters and interactions</li>
                <li>Test responsiveness on mobile</li>
                <li>Publish and share your dashboard</li>
            </ol>
        </div>

        <div class="dashboard-preview">
            <h5>📊 Example Dashboard: Executive Overview</h5>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div class="dashboard-widget">
                    <div class="widget-title">Total Revenue</div>
                    <div class="widget-value">$127,450</div>
                    <div class="widget-change">↗ +12.5% vs last month</div>
                </div>

                <div class="dashboard-widget">
                    <div class="widget-title">New Customers</div>
                    <div class="widget-value">89</div>
                    <div class="widget-change">↗ ****% vs last month</div>
                </div>

                <div class="dashboard-widget">
                    <div class="widget-title">Conversion Rate</div>
                    <div class="widget-value">3.2%</div>
                    <div class="widget-change">↗ +0.4% vs last month</div>
                </div>

                <div class="dashboard-widget">
                    <div class="widget-title">Support Tickets</div>
                    <div class="widget-value">23</div>
                    <div class="widget-change">↘ -15.2% vs last month</div>
                </div>
            </div>
        </div>

        <h2>🤖 AI-Powered Data Insights</h2>

        <div class="step-by-step">
            <h4><span class="step-number">3</span>Generating Automated Insights</h4>
            <ol>
                <li>Navigate to <strong>Data Insights</strong></li>
                <li>Select your data source</li>
                <li>Choose insight types to generate</li>
                <li>Configure analysis parameters</li>
                <li>Run the AI analysis</li>
                <li>Review generated insights</li>
                <li>Save important findings</li>
                <li>Set up automated insight generation</li>
            </ol>
        </div>

        <div class="ai-insights">
            <h5>🧠 AI-Generated Insights Example</h5>

            <div class="insight-item">
                <strong>📈 Trend Alert:</strong> Sales have increased by 23% over the last 3 months, with the highest growth in the "Electronics" category.
            </div>

            <div class="insight-item">
                <strong>⚠️ Anomaly Detected:</strong> Customer support tickets spiked 45% on March 15th, likely due to the product launch.
            </div>

            <div class="insight-item">
                <strong>🎯 Recommendation:</strong> Consider increasing inventory for "Wireless Headphones" - demand is 30% higher than supply.
            </div>

            <div class="insight-item">
                <strong>🔮 Prediction:</strong> Based on current trends, revenue is projected to reach $150,000 next month (+18%).
            </div>
        </div>

        <h2>⏰ Automated Report Scheduling</h2>

        <div class="step-by-step">
            <h4><span class="step-number">4</span>Setting Up Scheduled Reports</h4>
            <ol>
                <li>Navigate to <strong>Scheduled Reports</strong></li>
                <li>Click <strong>"Schedule New Report"</strong></li>
                <li>Select the report to schedule</li>
                <li>Choose frequency (daily, weekly, monthly)</li>
                <li>Set delivery time and timezone</li>
                <li>Configure email recipients</li>
                <li>Choose export format (PDF, Excel, CSV)</li>
                <li>Test and activate the schedule</li>
            </ol>
        </div>

        <div class="report-example">
            <h5>📅 Example: Weekly Sales Report Schedule</h5>
            <table>
                <tr>
                    <th>Setting</th>
                    <th>Configuration</th>
                </tr>
                <tr>
                    <td>Report</td>
                    <td>Weekly Sales Summary</td>
                </tr>
                <tr>
                    <td>Frequency</td>
                    <td>Every Monday at 9:00 AM</td>
                </tr>
                <tr>
                    <td>Recipients</td>
                    <td><EMAIL>, <EMAIL></td>
                </tr>
                <tr>
                    <td>Format</td>
                    <td>PDF with Excel attachment</td>
                </tr>
                <tr>
                    <td>Filters</td>
                    <td>Previous week's data only</td>
                </tr>
            </table>
        </div>

        <h2>📊 Advanced Analytics Features</h2>

        <div class="feature-grid">
            <div class="feature-card">
                <h4>🔍 Drill-Down Analysis</h4>
                <p>Click on any chart element to dive deeper into the data</p>
                <ul>
                    <li>Interactive chart exploration</li>
                    <li>Multi-level data drilling</li>
                    <li>Context-aware filtering</li>
                    <li>Breadcrumb navigation</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>📈 Trend Analysis</h4>
                <p>Automatic detection of patterns and trends in your data</p>
                <ul>
                    <li>Seasonal pattern detection</li>
                    <li>Growth rate calculations</li>
                    <li>Correlation analysis</li>
                    <li>Forecasting models</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>⚡ Real-Time Streaming</h4>
                <p>Live data updates without page refresh</p>
                <ul>
                    <li>WebSocket connections</li>
                    <li>Configurable refresh rates</li>
                    <li>Data change notifications</li>
                    <li>Performance optimization</li>
                </ul>
            </div>

            <div class="feature-card">
                <h4>🔗 Data Connections</h4>
                <p>Connect to multiple data sources and external APIs</p>
                <ul>
                    <li>Multiple table joins</li>
                    <li>External API integration</li>
                    <li>Google Sheets sync</li>
                    <li>Database connections</li>
                </ul>
            </div>
        </div>

        <div class="tip-box">
            <h5>💡 Analytics Best Practices</h5>
            <ul>
                <li><strong>Start with Questions:</strong> Define what you want to learn before building reports</li>
                <li><strong>Keep it Simple:</strong> Focus on key metrics that drive decisions</li>
                <li><strong>Use Appropriate Charts:</strong> Match visualization type to data type</li>
                <li><strong>Regular Reviews:</strong> Schedule periodic review of your analytics setup</li>
                <li><strong>Mobile-First:</strong> Ensure dashboards work well on mobile devices</li>
                <li><strong>Performance Monitoring:</strong> Watch for slow-loading reports and optimize</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: Data Privacy & Security</h5>
            <p>When sharing dashboards and reports, ensure sensitive data is properly protected. Use role-based access controls and consider data anonymization for public reports.</p>
        </div>

        <div class="navigation-buttons">
            <a href="workflow-automation-tutorial.html" class="nav-button">← Previous: Workflow Automation</a>
            <a href="integrations-tutorial.html" class="nav-button">Next: Integrations & Payments →</a>
        </div>
    </div>
</body>
</html>
