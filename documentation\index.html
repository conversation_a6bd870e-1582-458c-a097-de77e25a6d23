<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - Documentation</title>
    <!-- Add a favicon -->
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 30px;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        code {
            background-color: #f8f8f8;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            color: #e74c3c;
        }
        pre {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        .note {
            background-color: #f1f9ff;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff9f1;
            border-left: 4px solid #e67e22;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 15px 0;
        }
        .toc {
            background-color: #f8f8f8;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc li {
            margin-bottom: 5px;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .section {
            margin-bottom: 40px;
        }
        .screenshot {
            text-align: center;
            margin: 20px 0;
        }
        .screenshot img {
            max-width: 800px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .shortcode {
            font-weight: bold;
            background-color: #e8f4fc;
            padding: 2px 5px;
            border-radius: 3px;
        }

        /* New Documentation Styles */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 40px;
        }
        .hero-section h1 {
            margin: 0 0 15px 0;
            font-size: 2.5em;
        }
        .hero-subtitle {
            font-size: 1.3em;
            margin-bottom: 25px;
            opacity: 0.9;
        }
        .version-info {
            margin-bottom: 30px;
        }
        .version-badge, .phase-badge {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin: 0 10px;
            font-weight: bold;
        }
        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .tutorial-grid {
            margin: 40px 0;
        }
        .tutorial-section {
            margin-bottom: 40px;
        }
        .tutorial-section h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .tutorial-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .tutorial-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tutorial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            text-decoration: none;
            color: inherit;
        }
        .card-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        .tutorial-card h4 {
            color: #2980b9;
            margin: 0 0 10px 0;
        }
        .tutorial-card p {
            color: #6c757d;
            margin: 0;
        }

        .quick-reference {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 40px 0;
        }
        .reference-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .reference-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .reference-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .reference-card ul {
            list-style: none;
            padding: 0;
        }
        .reference-card li {
            margin-bottom: 8px;
        }
        .reference-card a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        .reference-card a:hover {
            color: #2980b9;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <h1>📚 Database App Builder Documentation</h1>
        <p class="hero-subtitle">Complete guide to building powerful database applications with WordPress</p>
        <div class="version-info">
            <span class="version-badge">Version 3.0</span>
            <span class="phase-badge">Phase 3 Complete</span>
        </div>
        <div class="hero-stats">
            <div class="stat-item">
                <div class="stat-number">8+</div>
                <div class="stat-label">Detailed Tutorials</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">200+</div>
                <div class="stat-label">Step-by-Step Instructions</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100+</div>
                <div class="stat-label">Examples & Use Cases</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">50+</div>
                <div class="stat-label">Features Covered</div>
            </div>
        </div>
    </div>

    <div class="tutorial-grid">
        <h2>🎓 Complete Tutorial Library</h2>

        <div class="tutorial-section">
            <h3>🚀 Getting Started</h3>
            <div class="tutorial-cards">
                <a href="complete-user-guide.html" class="tutorial-card">
                    <div class="card-icon">🎯</div>
                    <h4>Complete User Guide</h4>
                    <p>Master every feature from basics to advanced</p>
                </a>
                <a href="field-types-tutorial.html" class="tutorial-card">
                    <div class="card-icon">📝</div>
                    <h4>Field Types Tutorial</h4>
                    <p>Learn all 50+ field types with examples</p>
                </a>
                <a href="advanced-fields-tutorial.html" class="tutorial-card">
                    <div class="card-icon">🎓</div>
                    <h4>Advanced Fields</h4>
                    <p>Master complex fields like formulas & lookups</p>
                </a>
            </div>
        </div>

        <div class="tutorial-section">
            <h3>📋 Forms & Data Collection</h3>
            <div class="tutorial-cards">
                <a href="forms-tutorial.html" class="tutorial-card">
                    <div class="card-icon">📋</div>
                    <h4>Forms Builder Mastery</h4>
                    <p>Create powerful forms with validation, multi-step forms, and automation</p>
                </a>
                <a href="workflow-automation-tutorial.html" class="tutorial-card">
                    <div class="card-icon">⚡</div>
                    <h4>Workflow Automation</h4>
                    <p>Automate processes with visual workflow builder and approval systems</p>
                </a>
            </div>
        </div>

        <div class="tutorial-section">
            <h3>📊 Analytics & Intelligence <span class="phase-badge">Phase 3</span></h3>
            <div class="tutorial-cards">
                <a href="analytics-tutorial.html" class="tutorial-card">
                    <div class="card-icon">📈</div>
                    <h4>Analytics & Reporting</h4>
                    <p>Build enterprise-level analytics dashboards with AI-powered insights</p>
                </a>
            </div>
        </div>

        <div class="tutorial-section">
            <h3>🛒 E-commerce & Integration</h3>
            <div class="tutorial-cards">
                <a href="woocommerce-tutorial.html" class="tutorial-card">
                    <div class="card-icon">🛒</div>
                    <h4>WooCommerce Integration</h4>
                    <p>Enhance your e-commerce store with custom fields and advanced reporting</p>
                </a>
            </div>
        </div>

        <div class="tutorial-section">
            <h3>👥 User Management & Communication</h3>
            <div class="tutorial-cards">
                <a href="chat-system-tutorial.html" class="tutorial-card">
                    <div class="card-icon">💬</div>
                    <h4>Chat System</h4>
                    <p>Build real-time communication with group chat and file sharing</p>
                </a>
                <a href="user-management-tutorial.html" class="tutorial-card">
                    <div class="card-icon">👥</div>
                    <h4>User Management</h4>
                    <p>Manage frontend users, permissions, and access controls</p>
                </a>
            </div>
        </div>

        <div class="tutorial-section">
            <h3>🔧 Support & Troubleshooting</h3>
            <div class="tutorial-cards">
                <a href="troubleshooting-guide.html" class="tutorial-card">
                    <div class="card-icon">🔧</div>
                    <h4>Troubleshooting Guide</h4>
                    <p>Solve common issues and get expert help</p>
                </a>
            </div>
        </div>
    </div>

    <div class="quick-reference">
        <h2>📖 Quick Reference</h2>
        <div class="reference-grid">
            <div class="reference-card">
                <h4>🏗️ Basic Setup</h4>
                <ul>
                    <li><a href="#installation">Installation Guide</a></li>
                    <li><a href="#tables">Creating Tables</a></li>
                    <li><a href="#fields">Managing Fields</a></li>
                    <li><a href="#getting-started">First Steps</a></li>
                </ul>
            </div>
            <div class="reference-card">
                <h4>🎨 Building Apps</h4>
                <ul>
                    <li><a href="#forms">Building Forms</a></li>
                    <li><a href="#views">Creating Views</a></li>
                    <li><a href="#dashboards">Building Dashboards</a></li>
                    <li><a href="#shortcodes">Shortcodes Reference</a></li>
                </ul>
            </div>
            <div class="reference-card">
                <h4>⚙️ Advanced Features</h4>
                <ul>
                    <li><a href="#approval-workflows">Approval Workflows</a></li>
                    <li><a href="#permissions">Role-Based Permissions</a></li>
                    <li><a href="#integrations">Third-Party Integrations</a></li>
                    <li><a href="#payment-gateways">Payment Gateways</a></li>
                </ul>
            </div>
            <div class="reference-card">
                <h4>🆘 Help & Support</h4>
                <ul>
                    <li><a href="#faq">Frequently Asked Questions</a></li>
                    <li><a href="#troubleshooting">Troubleshooting</a></li>
                    <li><a href="troubleshooting-guide.html">Complete Troubleshooting Guide</a></li>
                    <li><a href="mailto:<EMAIL>">Contact Support</a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section" id="introduction">
        <h2>Introduction</h2>
        <p>Database App Builder is a powerful WordPress plugin that allows you to create custom database applications without writing a single line of code. With an intuitive interface, you can build forms, views, dashboards, and approval workflows to manage your data efficiently.</p>

        <h3>Key Features</h3>
        <ul>
            <li><strong>Custom Database Tables</strong>: Create and manage custom database tables with ease</li>
            <li><strong>Form Builder</strong>: Build forms to collect and manage data</li>
            <li><strong>View Builder</strong>: Create custom views to display your data</li>
            <li><strong>Dashboard Builder</strong>: Build interactive dashboards to visualize your data</li>
            <li><strong>Approval Workflows</strong>: Set up multi-level approval processes</li>
            <li><strong>Role-Based Permissions</strong>: Control who can view and edit your data</li>
            <li><strong>Conditional Logic</strong>: Create dynamic forms with conditional logic</li>
            <li><strong>Relationships</strong>: Create relationships between tables</li>
            <li><strong>Formula Fields</strong>: Calculate values automatically</li>
            <li><strong>Payment Integration</strong>: Accept payments with Stripe, PayPal, and Paystack</li>
            <li><strong>Third-Party Integrations</strong>: Connect with Google Sheets, Zapier, and more</li>
        </ul>
    </div>

    <div class="section" id="installation">
        <h2>Installation</h2>
        <ol>
            <li>Download the plugin from ThemeForest</li>
            <li>Log in to your WordPress admin panel</li>
            <li>Navigate to <strong>Plugins > Add New > Upload Plugin</strong></li>
            <li>Choose the downloaded zip file and click <strong>Install Now</strong></li>
            <li>After installation, click <strong>Activate Plugin</strong></li>
            <li>Navigate to <strong>Database App Builder</strong> in the admin menu to start building your applications</li>
        </ol>

        <div class="note">
            <p><strong>Note:</strong> The plugin requires WordPress 5.0 or higher and PHP 7.0 or higher.</p>
        </div>
    </div>

    <div class="section" id="getting-started">
        <h2>Getting Started</h2>
        <p>After activating the plugin, you'll see a new menu item called "Database App Builder" in your WordPress admin panel. This is your starting point for creating database applications.</p>

        <h3>Plugin Dashboard</h3>
        <p>The plugin dashboard provides an overview of your database applications, including:</p>
        <ul>
            <li>Number of tables, forms, views, and dashboards</li>
            <li>Recent activity</li>
            <li>Quick links to create new items</li>
            <li>System status information</li>
        </ul>

        <h3>Typical Workflow</h3>
        <p>A typical workflow for creating a database application includes:</p>
        <ol>
            <li>Create a table to store your data</li>
            <li>Add fields to the table</li>
            <li>Create a form to collect data</li>
            <li>Create views to display data</li>
            <li>Build dashboards to visualize data</li>
            <li>Set up approval workflows (if needed)</li>
            <li>Configure permissions</li>
            <li>Use shortcodes to embed forms, views, and dashboards on your site</li>
        </ol>
    </div>

    <div class="section" id="tables">
        <h2>Creating Tables</h2>
        <p>Tables are the foundation of your database applications. They store the data that you collect through forms.</p>

        <h3>To create a new table:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Tables</strong></li>
            <li>Click the <strong>Add New Table</strong> button</li>
            <li>Enter a table label (e.g., "Customers")</li>
            <li>The table slug will be generated automatically (e.g., "customers")</li>
            <li>Add an optional description</li>
            <li>Click <strong>Create Table</strong></li>
        </ol>

        <div class="note">
            <p><strong>Note:</strong> The plugin will automatically create a database table with the prefix <code>wp_dab_</code> followed by the table slug (e.g., <code>wp_dab_customers</code>).</p>
        </div>

        <h3>Table Management</h3>
        <p>After creating a table, you can:</p>
        <ul>
            <li><strong>Edit</strong> the table label and description</li>
            <li><strong>Delete</strong> the table (this will permanently delete all data in the table)</li>
            <li><strong>View</strong> the table structure and data</li>
            <li><strong>Export</strong> the table data to CSV, Excel, or PDF</li>
        </ul>
    </div>

    <div class="section" id="fields">
        <h2>Managing Fields</h2>
        <p>Fields define the structure of your tables and the data they can store.</p>

        <h3>To add fields to a table:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Fields</strong></li>
            <li>Select the table you want to add fields to</li>
            <li>Click the <strong>Add New Field</strong> button</li>
            <li>Enter a field label (e.g., "First Name")</li>
            <li>The field slug will be generated automatically (e.g., "first_name")</li>
            <li>Select the field type</li>
            <li>Configure field options (required, placeholder, etc.)</li>
            <li>Click <strong>Add Field</strong></li>
        </ol>

        <h3>Available Field Types</h3>
        <table>
            <tr>
                <th>Field Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>Text</td>
                <td>Single line text input</td>
            </tr>
            <tr>
                <td>Textarea</td>
                <td>Multi-line text input</td>
            </tr>
            <tr>
                <td>Number</td>
                <td>Numeric input</td>
            </tr>
            <tr>
                <td>Email</td>
                <td>Email address input with validation</td>
            </tr>
            <tr>
                <td>Date</td>
                <td>Date picker</td>
            </tr>
            <tr>
                <td>Select</td>
                <td>Dropdown select with options</td>
            </tr>
            <tr>
                <td>Checkbox</td>
                <td>Single checkbox (boolean)</td>
            </tr>
            <tr>
                <td>Radio</td>
                <td>Radio button group</td>
            </tr>
            <tr>
                <td>File</td>
                <td>File upload</td>
            </tr>
            <tr>
                <td>Image</td>
                <td>Image upload with preview</td>
            </tr>
            <tr>
                <td>Relationship</td>
                <td>Reference to another table</td>
            </tr>
            <tr>
                <td>Lookup</td>
                <td>Dropdown populated from another table</td>
            </tr>
            <tr>
                <td>Formula</td>
                <td>Calculated field based on other fields</td>
            </tr>
            <tr>
                <td>Autoincrement</td>
                <td>Automatically incremented number</td>
            </tr>
            <tr>
                <td>Currency</td>
                <td>Currency input with formatting</td>
            </tr>
            <tr>
                <td>Signature</td>
                <td>Digital signature capture</td>
            </tr>
            <tr>
                <td>Media</td>
                <td>Audio/video media field</td>
            </tr>
            <tr>
                <td>Social Media</td>
                <td>Social media links</td>
            </tr>
            <tr>
                <td>Multiselect</td>
                <td>Multiple selection dropdown</td>
            </tr>
            <tr>
                <td>Conditional Logic</td>
                <td>Field that changes based on conditions</td>
            </tr>
        </table>

        <h3>Advanced Field Options</h3>
        <p>Depending on the field type, you may have additional options to configure:</p>
        <ul>
            <li><strong>Required</strong>: Make the field mandatory</li>
            <li><strong>Placeholder</strong>: Add placeholder text</li>
            <li><strong>Default Value</strong>: Set a default value</li>
            <li><strong>Options</strong>: For select, radio, and checkbox fields</li>
            <li><strong>Validation</strong>: Add custom validation rules</li>
            <li><strong>Conditional Logic</strong>: Show/hide based on other fields</li>
        </ul>
    </div>

    <div class="section" id="forms">
        <h2>Building Forms</h2>
        <p>Forms allow you to collect data for your tables. You can create multiple forms for a single table, each with different fields and settings.</p>

        <h3>To create a new form:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Forms</strong></li>
            <li>Click the <strong>Add New Form</strong> button</li>
            <li>Enter a form name</li>
            <li>Select the table that will store the form data</li>
            <li>Select the fields to include in the form</li>
            <li>Configure form settings (success message, redirect, etc.)</li>
            <li>Click <strong>Create Form</strong></li>
        </ol>

        <h3>Form Settings</h3>
        <ul>
            <li><strong>Form Title</strong>: Display a title at the top of the form</li>
            <li><strong>Form Description</strong>: Add a description below the title</li>
            <li><strong>Success Message</strong>: Message displayed after successful submission</li>
            <li><strong>Submit Button Text</strong>: Customize the submit button text</li>
            <li><strong>Redirect URL</strong>: Redirect to a specific page after submission</li>
            <li><strong>Email Notification</strong>: Send email notifications on form submission</li>
        </ul>

        <h3>Conditional Logic</h3>
        <p>You can add conditional logic to your forms to show or hide fields based on the values of other fields.</p>
        <ol>
            <li>In the form editor, click the <strong>Conditional Logic</strong> tab</li>
            <li>Click <strong>Add Rule</strong></li>
            <li>Select the target field (the field to show/hide)</li>
            <li>Select the condition (e.g., "is equal to", "is not equal to", etc.)</li>
            <li>Select the source field and value</li>
            <li>Click <strong>Save Rules</strong></li>
        </ol>

        <h3>Using Forms on Your Site</h3>
        <p>To display a form on your site, use the <span class="shortcode">[dab_form id="X"]</span> shortcode, where X is the form ID.</p>
        <pre><code>[dab_form id="1"]</code></pre>

        <div class="note">
            <p><strong>Note:</strong> You can find the form ID in the Forms list or in the form editor.</p>
        </div>
    </div>

    <div class="section" id="views">
        <h2>Creating Views</h2>
        <p>Views allow you to display data from your tables on the frontend of your site. You can create multiple views for a single table, each with different fields, filters, and sorting options.</p>

        <h3>To create a new view:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Views</strong></li>
            <li>Click the <strong>Add New View</strong> button</li>
            <li>Enter a view name</li>
            <li>Select the table to display data from</li>
            <li>Select the fields to include in the view</li>
            <li>Configure view settings (filters, sorting, etc.)</li>
            <li>Click <strong>Create View</strong></li>
        </ol>

        <h3>View Settings</h3>
        <ul>
            <li><strong>Selected Fields</strong>: Choose which fields to display</li>
            <li><strong>Filter Conditions</strong>: Add filters to limit the data displayed</li>
            <li><strong>Sort Order</strong>: Set the default sorting order</li>
            <li><strong>Public Access</strong>: Allow non-logged-in users to view the data</li>
        </ul>

        <h3>Using Views on Your Site</h3>
        <p>To display a view on your site, use the <span class="shortcode">[dab_view id="X"]</span> shortcode, where X is the view ID.</p>
        <pre><code>[dab_view id="1"]</code></pre>

        <div class="note">
            <p><strong>Note:</strong> You can find the view ID in the Views list or in the view editor.</p>
        </div>

        <h3>Advanced View Features</h3>
        <ul>
            <li><strong>Search</strong>: Add a search box to filter data</li>
            <li><strong>Pagination</strong>: Split data into pages</li>
            <li><strong>Sorting</strong>: Allow users to sort data by clicking column headers</li>
            <li><strong>Export</strong>: Add buttons to export data to CSV, Excel, or PDF</li>
            <li><strong>Inline Editing</strong>: Allow users to edit data directly in the view</li>
        </ul>
    </div>

    <div class="section" id="dashboards">
        <h2>Building Dashboards</h2>
        <p>Dashboards allow you to visualize your data with charts, graphs, and other widgets. You can create multiple dashboards, each with different widgets and layouts.</p>

        <h3>To create a new dashboard:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Dashboards</strong></li>
            <li>Click the <strong>Add New Dashboard</strong> button</li>
            <li>Enter a dashboard name</li>
            <li>Click <strong>Create Dashboard</strong></li>
            <li>In the dashboard builder, add widgets by dragging them from the sidebar</li>
            <li>Configure each widget's settings</li>
            <li>Click <strong>Save Dashboard</strong></li>
        </ol>

        <h3>Available Widgets</h3>
        <ul>
            <li><strong>Chart</strong>: Display data as a chart (bar, line, pie, etc.)</li>
            <li><strong>Counter</strong>: Display a count of records</li>
            <li><strong>Table</strong>: Display data in a table</li>
            <li><strong>List</strong>: Display data as a list</li>
            <li><strong>Calendar</strong>: Display data on a calendar</li>
            <li><strong>Map</strong>: Display data on a map</li>
            <li><strong>Text</strong>: Add custom text or HTML</li>
        </ul>

        <h3>Using Dashboards on Your Site</h3>
        <p>To display a dashboard on your site, use the <span class="shortcode">[dab_dashboard id="X"]</span> shortcode, where X is the dashboard ID.</p>
        <pre><code>[dab_dashboard id="1"]</code></pre>

        <div class="note">
            <p><strong>Note:</strong> You can find the dashboard ID in the Dashboards list or in the dashboard editor.</p>
        </div>
    </div>

    <div class="section" id="approval-workflows">
        <h2>Approval Workflows</h2>
        <p>Approval workflows allow you to set up multi-level approval processes for your data. This is useful for scenarios where data needs to be reviewed and approved before it becomes visible or active.</p>

        <h3>To set up an approval workflow:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Approval Workflows</strong></li>
            <li>Click the <strong>Add New Workflow</strong> button</li>
            <li>Select the table to apply the workflow to</li>
            <li>Add approval levels (e.g., "Manager Approval", "Director Approval")</li>
            <li>For each level, select the user roles that can approve</li>
            <li>Configure notification settings</li>
            <li>Click <strong>Save Workflow</strong></li>
        </ol>

        <h3>Approval Process</h3>
        <p>When a new record is submitted through a form with an approval workflow:</p>
        <ol>
            <li>The record is marked as "Pending" and assigned to the first approval level</li>
            <li>Users with the appropriate role for that level can approve or reject the record</li>
            <li>If approved, the record moves to the next level (if any)</li>
            <li>If rejected, the record is marked as "Rejected" and the process stops</li>
            <li>When all levels have approved, the record is marked as "Approved"</li>
        </ol>

        <h3>Approval Dashboard</h3>
        <p>Users can view and manage records that need their approval using the approval dashboard.</p>
        <p>To display the approval dashboard on your site, use the <span class="shortcode">[dab_approval_dashboard]</span> shortcode.</p>
        <pre><code>[dab_approval_dashboard]</code></pre>

        <div class="note">
            <p><strong>Note:</strong> Users will only see records that they have permission to approve.</p>
        </div>
    </div>

    <div class="section" id="permissions">
        <h2>Role-Based Permissions</h2>
        <p>Role-based permissions allow you to control who can view, add, edit, and delete data in your tables. You can set permissions at the table level and at the field level.</p>

        <h3>To configure table permissions:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Permissions Dashboard</strong></li>
            <li>Select the table you want to configure permissions for</li>
            <li>For each WordPress role, set the following permissions:</li>
            <ul>
                <li><strong>View</strong>: Can view records</li>
                <li><strong>Add</strong>: Can add new records</li>
                <li><strong>Edit</strong>: Can edit existing records</li>
                <li><strong>Delete</strong>: Can delete records</li>
                <li><strong>Export</strong>: Can export data</li>
            </ul>
            <li>Click <strong>Save Permissions</strong></li>
        </ol>

        <h3>To configure field permissions:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Fields</strong></li>
            <li>Select the table containing the field</li>
            <li>Click the <strong>Edit</strong> button for the field</li>
            <li>Click the <strong>Permissions</strong> tab</li>
            <li>For each WordPress role, set the following permissions:</li>
            <ul>
                <li><strong>View</strong>: Can view the field</li>
                <li><strong>Edit</strong>: Can edit the field</li>
            </ul>
            <li>Click <strong>Save Field</strong></li>
        </ol>

        <div class="note">
            <p><strong>Note:</strong> By default, only administrators have full access to all tables and fields.</p>
        </div>
    </div>

    <div class="section" id="integrations">
        <h2>Third-Party Integrations</h2>
        <p>Database App Builder integrates with several third-party services to extend its functionality.</p>

        <h3>Google Sheets Integration</h3>
        <p>The Google Sheets integration allows you to sync data between your tables and Google Sheets.</p>
        <ol>
            <li>Navigate to <strong>Database App Builder > Integrations</strong></li>
            <li>Click the <strong>Google Sheets</strong> tab</li>
            <li>Click <strong>Connect to Google Sheets</strong></li>
            <li>Follow the authentication process</li>
            <li>Create a new sync or edit an existing one:</li>
            <ul>
                <li>Select the table to sync</li>
                <li>Select the Google Sheet</li>
                <li>Map the fields</li>
                <li>Set the sync direction (one-way or two-way)</li>
                <li>Set the sync frequency</li>
            </ul>
            <li>Click <strong>Save Sync</strong></li>
        </ol>

        <h3>Zapier Integration</h3>
        <p>The Zapier integration allows you to connect your tables to thousands of other apps and services.</p>
        <ol>
            <li>Navigate to <strong>Database App Builder > Integrations</strong></li>
            <li>Click the <strong>Zapier</strong> tab</li>
            <li>Click <strong>Enable Zapier Integration</strong></li>
            <li>Copy the API key</li>
            <li>Go to Zapier and create a new Zap</li>
            <li>Select "Database App Builder" as the trigger app</li>
            <li>Configure the trigger (e.g., "New Record", "Updated Record")</li>
            <li>Enter your website URL and API key</li>
            <li>Select the table to trigger from</li>
            <li>Continue setting up your Zap as usual</li>
        </ol>
    </div>

    <div class="section" id="payment-gateways">
        <h2>Payment Gateways</h2>
        <p>Database App Builder supports several payment gateways to allow you to collect payments through your forms.</p>

        <h3>Supported Payment Gateways</h3>
        <ul>
            <li><strong>Stripe</strong>: Credit card payments</li>
            <li><strong>PayPal</strong>: PayPal payments</li>
            <li><strong>Paystack</strong>: African payment gateway</li>
        </ul>

        <h3>To configure payment gateways:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Payment Settings</strong></li>
            <li>Select the payment gateway you want to configure</li>
            <li>Enter your API keys and other settings</li>
            <li>Click <strong>Save Settings</strong></li>
        </ol>

        <h3>To add a payment field to a form:</h3>
        <ol>
            <li>Navigate to <strong>Database App Builder > Fields</strong></li>
            <li>Select the table for your form</li>
            <li>Click <strong>Add New Field</strong></li>
            <li>Enter a field label (e.g., "Payment")</li>
            <li>Select "Payment" as the field type</li>
            <li>Configure the payment settings:</li>
            <ul>
                <li>Payment gateway</li>
                <li>Amount (fixed or from another field)</li>
                <li>Currency</li>
                <li>Description</li>
                <li>Success and cancel URLs</li>
            </ul>
            <li>Click <strong>Add Field</strong></li>
        </ol>

        <h3>Payment Tracking</h3>
        <p>You can track payments in the <strong>Database App Builder > Payment Tracking</strong> page, which shows all payment transactions, their status, and details.</p>
    </div>

    <div class="section" id="shortcodes">
        <h2>Shortcodes Reference</h2>
        <p>Database App Builder provides several shortcodes to display your forms, views, dashboards, and approval workflows on your site.</p>

        <h3>Form Shortcode</h3>
        <pre><code>[dab_form id="X"]</code></pre>
        <p>Displays a form where X is the form ID.</p>

        <h3>View Shortcode</h3>
        <pre><code>[dab_view id="X"]</code></pre>
        <p>Displays a view where X is the view ID.</p>

        <h3>Dashboard Shortcode</h3>
        <pre><code>[dab_dashboard id="X"]</code></pre>
        <p>Displays a dashboard where X is the dashboard ID.</p>

        <h3>Approval Dashboard Shortcode</h3>
        <pre><code>[dab_approval_dashboard]</code></pre>
        <p>Displays the approval dashboard for the current user.</p>

        <h3>Advanced Shortcode Parameters</h3>
        <p>You can add additional parameters to the shortcodes to customize their behavior:</p>

        <h4>Form Shortcode Parameters</h4>
        <ul>
            <li><code>redirect="URL"</code>: Redirect to a specific URL after submission</li>
            <li><code>success_message="Text"</code>: Custom success message</li>
            <li><code>button_text="Text"</code>: Custom submit button text</li>
        </ul>

        <h4>View Shortcode Parameters</h4>
        <ul>
            <li><code>filter_field="field_slug"</code>: Field to filter by</li>
            <li><code>filter_value="value"</code>: Value to filter by</li>
            <li><code>sort_field="field_slug"</code>: Field to sort by</li>
            <li><code>sort_order="ASC|DESC"</code>: Sort order</li>
            <li><code>limit="X"</code>: Limit the number of records displayed</li>
        </ul>
    </div>

    <div class="section" id="faq">
        <h2>Frequently Asked Questions</h2>

        <h3>Do I need to know how to code to use this plugin?</h3>
        <p>No, Database App Builder is designed to be used without any coding knowledge. The intuitive interface allows you to create complex database applications with just a few clicks.</p>

        <h3>Can I create relationships between tables?</h3>
        <p>Yes, you can create one-to-many and many-to-many relationships between tables. This allows you to create complex data structures.</p>

        <h3>Is my data secure?</h3>
        <p>Yes, all data is stored in your WordPress database and is protected by the same security measures as your WordPress site. The plugin also includes role-based permissions to control who can view and edit your data.</p>

        <h3>Can I export my data?</h3>
        <p>Yes, you can export your data to CSV, Excel, and PDF formats.</p>

        <h3>Does it work with any theme?</h3>
        <p>Yes, Database App Builder is designed to work with any WordPress theme.</p>

        <h3>Can I use the plugin on multiple sites?</h3>
        <p>This depends on the license you purchased. Please check your license details on ThemeForest.</p>

        <h3>How do I get support?</h3>
        <p>You can get support by:</p>
        <ul>
            <li>Checking this documentation</li>
            <li>Contacting us through our support system on ThemeForest</li>
            <li>Emailing <NAME_EMAIL></li>
        </ul>
    </div>

    <div class="section" id="troubleshooting">
        <h2>Troubleshooting</h2>

        <h3>Common Issues and Solutions</h3>

        <h4>Forms not displaying</h4>
        <ul>
            <li>Make sure you're using the correct shortcode with the correct ID</li>
            <li>Check if the form is active</li>
            <li>Check if there are any JavaScript errors in the browser console</li>
            <li>Try disabling other plugins to check for conflicts</li>
        </ul>

        <h4>Data not saving</h4>
        <ul>
            <li>Check if the form has the correct table selected</li>
            <li>Check if the fields are mapped correctly</li>
            <li>Check if there are any validation errors</li>
            <li>Check if the user has permission to add records</li>
        </ul>

        <h4>Views not displaying data</h4>
        <ul>
            <li>Make sure there is data in the table</li>
            <li>Check if the view has the correct table selected</li>
            <li>Check if the filter conditions are too restrictive</li>
            <li>Check if the user has permission to view the data</li>
        </ul>

        <h4>Payment gateway not working</h4>
        <ul>
            <li>Check if the payment gateway is configured correctly</li>
            <li>Check if the API keys are valid</li>
            <li>Check if the payment field is configured correctly</li>
            <li>Try testing with a small amount</li>
        </ul>

        <h3>Debug Mode</h3>
        <p>If you're experiencing issues, you can enable debug mode to get more detailed error messages:</p>
        <ol>
            <li>Navigate to <strong>Database App Builder > Settings</strong></li>
            <li>Click the <strong>Advanced</strong> tab</li>
            <li>Check the <strong>Enable Debug Mode</strong> option</li>
            <li>Click <strong>Save Settings</strong></li>
        </ol>

        <div class="warning">
            <p><strong>Warning:</strong> Debug mode may display sensitive information. Only enable it temporarily and disable it when you're done troubleshooting.</p>
        </div>
    </div>
</body>
</html>
