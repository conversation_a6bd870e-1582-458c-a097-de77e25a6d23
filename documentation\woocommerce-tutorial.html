<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database App Builder - WooCommerce Integration Tutorial</title>
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 30px;
        }
        h1 {
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: -30px -30px 30px -30px;
        }
        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
        }
        h3 {
            color: #2980b9;
            border-left: 3px solid #3498db;
            padding-left: 15px;
        }
        .step-by-step {
            background: #f1f9ff;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .step-by-step h4 {
            color: #2980b9;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .example-box {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .example-box h5 {
            color: #27ae60;
            margin-top: 0;
            font-weight: bold;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box h5 {
            color: #856404;
            margin-top: 0;
            font-weight: bold;
        }
        .tip-box {
            background: #d1ecf1;
            border: 1px solid #17a2b8;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .tip-box h5 {
            color: #0c5460;
            margin-top: 0;
            font-weight: bold;
        }
        .woocommerce-box {
            background: linear-gradient(135deg, #96588a 0%, #7b4397 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .woocommerce-box h5 {
            color: white;
            margin-top: 0;
            font-weight: bold;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        .feature-card h4 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 2px solid #dee2e6;
        }
        .nav-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 WooCommerce Integration Tutorial</h1>
        <p style="text-align: center; font-size: 1.2em; color: #6c757d; margin-bottom: 30px;">
            Supercharge your WooCommerce store with advanced database functionality
        </p>

        <div class="section" id="woocommerce-overview">
            <h2>🛒 WooCommerce Integration Overview</h2>
            <p>Database App Builder seamlessly integrates with WooCommerce to extend your e-commerce capabilities with custom fields, advanced reporting, and enhanced customer management. The integration is automatically available when WooCommerce is installed and active.</p>

            <div class="woocommerce-box">
                <h5>🚀 What You Get with WooCommerce Integration:</h5>
                <ul>
                    <li><strong>Custom Product Fields:</strong> Add unlimited custom fields to products</li>
                    <li><strong>Enhanced Order Management:</strong> Custom order fields and workflows</li>
                    <li><strong>Advanced Customer Data:</strong> Extended customer profiles and analytics</li>
                    <li><strong>Sales Dashboards:</strong> Real-time sales analytics and reporting</li>
                    <li><strong>Inventory Management:</strong> Advanced stock tracking and alerts</li>
                    <li><strong>Marketing Automation:</strong> Customer segmentation and campaigns</li>
                    <li><strong>Enhanced Checkout:</strong> Custom checkout fields and processes</li>
                    <li><strong>Detailed Reports:</strong> Advanced sales and customer reports</li>
                </ul>
            </div>

            <div class="warning-box">
                <h5>⚠️ Prerequisites:</h5>
                <ul>
                    <li>WooCommerce plugin must be installed and activated</li>
                    <li>WooCommerce version 3.0 or higher recommended</li>
                    <li>Database App Builder must be activated after WooCommerce</li>
                    <li>Proper WordPress user permissions for WooCommerce management</li>
                </ul>
            </div>
        </div>

        <div class="section" id="woocommerce-features">
            <h2>🎯 WooCommerce Integration Features</h2>
            <p>The WooCommerce integration adds a dedicated section to your Database App Builder menu with specialized tools:</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🛒 Sales Dashboard</h4>
                    <p>Real-time sales analytics with revenue tracking, conversion rates, and performance metrics.</p>
                    <p><strong>Location:</strong> Database App Builder → 🛒 Sales Dashboard</p>
                </div>
                <div class="feature-card">
                    <h4>📦 Product Fields</h4>
                    <p>Add custom fields to products for specifications, variants, and additional data.</p>
                    <p><strong>Location:</strong> Database App Builder → 📦 Product Fields</p>
                </div>
                <div class="feature-card">
                    <h4>📋 Order Fields</h4>
                    <p>Extend orders with custom fields for special instructions, delivery preferences, and more.</p>
                    <p><strong>Location:</strong> Database App Builder → 📋 Order Fields</p>
                </div>
                <div class="feature-card">
                    <h4>👤 Customer Data</h4>
                    <p>Enhanced customer profiles with custom fields, purchase history, and behavior analytics.</p>
                    <p><strong>Location:</strong> Database App Builder → 👤 Customer Data</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Inventory Management</h4>
                    <p>Advanced stock tracking, low stock alerts, and inventory analytics.</p>
                    <p><strong>Location:</strong> Database App Builder → 📊 Inventory Management</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Advanced Checkout</h4>
                    <p>Custom checkout fields, conditional logic, and enhanced checkout processes.</p>
                    <p><strong>Location:</strong> Database App Builder → 💳 Advanced Checkout</p>
                </div>
                <div class="feature-card">
                    <h4>📧 Marketing Automation</h4>
                    <p>Customer segmentation, automated campaigns, and personalized marketing.</p>
                    <p><strong>Location:</strong> Database App Builder → 📧 Marketing Automation</p>
                </div>
                <div class="feature-card">
                    <h4>📈 Enhanced Reports</h4>
                    <p>Advanced sales reports, customer analytics, and business intelligence.</p>
                    <p><strong>Location:</strong> Database App Builder → 📈 Enhanced Reports</p>
                </div>
            </div>
        </div>

        <div class="section" id="sales-dashboard">
            <h2>🛒 Sales Dashboard</h2>
            <p>The Sales Dashboard provides comprehensive analytics for your WooCommerce store with real-time data and interactive charts.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Accessing the Sales Dashboard</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 🛒 Sales Dashboard</strong></li>
                    <li>The dashboard loads automatically with current data</li>
                    <li>Use date filters to view specific time periods</li>
                    <li>Click on charts for detailed breakdowns</li>
                    <li>Export reports in PDF, Excel, or CSV formats</li>
                </ol>

                <div class="example-box">
                    <h5>📊 Dashboard Widgets Include:</h5>
                    <ul>
                        <li><strong>Revenue Overview:</strong> Total revenue, growth trends, and comparisons</li>
                        <li><strong>Order Statistics:</strong> Order count, average order value, conversion rates</li>
                        <li><strong>Top Products:</strong> Best-selling products and categories</li>
                        <li><strong>Customer Metrics:</strong> New customers, returning customers, lifetime value</li>
                        <li><strong>Geographic Sales:</strong> Sales by country, state, or city</li>
                        <li><strong>Payment Methods:</strong> Breakdown of payment method usage</li>
                        <li><strong>Refunds & Returns:</strong> Refund rates and return analytics</li>
                        <li><strong>Traffic Sources:</strong> Sales attribution by traffic source</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="product-fields">
            <h2>📦 Custom Product Fields</h2>
            <p>Extend your WooCommerce products with unlimited custom fields for specifications, variants, and additional product data.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Adding Custom Product Fields</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 📦 Product Fields</strong></li>
                    <li>Click <strong>"Add New Product Field"</strong></li>
                    <li>Choose the field type (text, number, select, etc.)</li>
                    <li>Configure field properties and validation</li>
                    <li>Set display options (product page, admin, etc.)</li>
                    <li>Save the field configuration</li>
                    <li>The field will appear in product edit screens</li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Electronics Store Product Fields</h5>
                    <ul>
                        <li><strong>Technical Specifications:</strong> Processor, RAM, Storage (text fields)</li>
                        <li><strong>Warranty Period:</strong> 1 year, 2 years, 3 years (select field)</li>
                        <li><strong>Energy Rating:</strong> A+, A++, A+++ (select field)</li>
                        <li><strong>Dimensions:</strong> Length x Width x Height (text field)</li>
                        <li><strong>Weight:</strong> Product weight in kg (number field)</li>
                        <li><strong>Color Options:</strong> Available colors (multiselect field)</li>
                        <li><strong>Installation Required:</strong> Yes/No (checkbox field)</li>
                        <li><strong>User Manual:</strong> PDF upload (file field)</li>
                    </ul>
                </div>
            </div>

            <div class="step-by-step">
                <h4><span class="step-number">2</span>Field Display Options</h4>
                <p>Control where and how your custom fields appear:</p>

                <table>
                    <tr>
                        <th>Display Location</th>
                        <th>Description</th>
                        <th>Use Cases</th>
                    </tr>
                    <tr>
                        <td><strong>Product Page</strong></td>
                        <td>Show on frontend product pages</td>
                        <td>Specifications, features, details</td>
                    </tr>
                    <tr>
                        <td><strong>Product Listing</strong></td>
                        <td>Display in shop/category pages</td>
                        <td>Key features, price modifiers</td>
                    </tr>
                    <tr>
                        <td><strong>Cart/Checkout</strong></td>
                        <td>Show during checkout process</td>
                        <td>Delivery options, special instructions</td>
                    </tr>
                    <tr>
                        <td><strong>Order Details</strong></td>
                        <td>Include in order confirmation</td>
                        <td>Selected options, customizations</td>
                    </tr>
                    <tr>
                        <td><strong>Admin Only</strong></td>
                        <td>Visible only in admin area</td>
                        <td>Internal notes, supplier info</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="section" id="order-fields">
            <h2>📋 Custom Order Fields</h2>
            <p>Add custom fields to orders for special instructions, delivery preferences, and additional order data.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Creating Order Fields</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 📋 Order Fields</strong></li>
                    <li>Click <strong>"Add New Order Field"</strong></li>
                    <li>Select field type and configure properties</li>
                    <li>Choose when the field appears (checkout, admin, etc.)</li>
                    <li>Set validation rules and requirements</li>
                    <li>Configure email inclusion settings</li>
                    <li>Save and test the field</li>
                </ol>

                <div class="example-box">
                    <h5>📋 Example: Restaurant Order Fields</h5>
                    <ul>
                        <li><strong>Delivery Instructions:</strong> Special delivery notes (textarea)</li>
                        <li><strong>Preferred Delivery Time:</strong> Time slot selection (select)</li>
                        <li><strong>Contact Phone:</strong> Alternative contact number (phone)</li>
                        <li><strong>Special Dietary Requirements:</strong> Allergies, preferences (multiselect)</li>
                        <li><strong>Gift Message:</strong> Message for gift orders (textarea)</li>
                        <li><strong>Packaging Preference:</strong> Eco-friendly, standard (radio)</li>
                        <li><strong>Order Source:</strong> Website, phone, app (hidden field)</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="customer-data">
            <h2>👤 Enhanced Customer Data</h2>
            <p>Extend customer profiles with custom fields and advanced analytics to better understand your customers.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Customer Profile Enhancement</h4>
                <ol>
                    <li>Navigate to <strong>Database App Builder → 👤 Customer Data</strong></li>
                    <li>View enhanced customer profiles with purchase history</li>
                    <li>Add custom fields for additional customer information</li>
                    <li>Set up customer segmentation rules</li>
                    <li>Configure automated customer insights</li>
                    <li>Export customer data for analysis</li>
                </ol>

                <div class="example-box">
                    <h5>👤 Enhanced Customer Features:</h5>
                    <ul>
                        <li><strong>Purchase History:</strong> Complete order timeline with analytics</li>
                        <li><strong>Customer Lifetime Value:</strong> Automatic CLV calculations</li>
                        <li><strong>Behavior Analytics:</strong> Shopping patterns and preferences</li>
                        <li><strong>Segmentation:</strong> Automatic customer grouping</li>
                        <li><strong>Communication History:</strong> Email and support interactions</li>
                        <li><strong>Loyalty Metrics:</strong> Repeat purchase rates and frequency</li>
                        <li><strong>Risk Assessment:</strong> Fraud detection and payment issues</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section" id="inventory-management">
            <h2>📊 Advanced Inventory Management</h2>
            <p>Take control of your inventory with advanced tracking, alerts, and analytics.</p>

            <div class="step-by-step">
                <h4><span class="step-number">1</span>Inventory Features</h4>
                <p>Access advanced inventory tools:</p>

                <div class="example-box">
                    <h5>📦 Inventory Management Features:</h5>
                    <ul>
                        <li><strong>Real-time Stock Tracking:</strong> Live inventory updates</li>
                        <li><strong>Low Stock Alerts:</strong> Automated notifications</li>
                        <li><strong>Reorder Points:</strong> Automatic reorder suggestions</li>
                        <li><strong>Stock Movement History:</strong> Complete audit trail</li>
                        <li><strong>Supplier Management:</strong> Track suppliers and costs</li>
                        <li><strong>Inventory Valuation:</strong> Stock value calculations</li>
                        <li><strong>Forecasting:</strong> Demand prediction and planning</li>
                        <li><strong>Multi-location Support:</strong> Track stock across locations</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tip-box">
            <h5>💡 Pro Tips: WooCommerce Integration</h5>
            <ul>
                <li><strong>Start Simple:</strong> Begin with basic custom fields before advanced features</li>
                <li><strong>Test Thoroughly:</strong> Test all custom fields in a staging environment</li>
                <li><strong>Monitor Performance:</strong> Watch for any impact on site speed</li>
                <li><strong>Backup Regularly:</strong> Always backup before making changes</li>
                <li><strong>Use Analytics:</strong> Leverage the enhanced reporting for business insights</li>
                <li><strong>Customer Experience:</strong> Ensure custom fields improve, not complicate, the user experience</li>
            </ul>
        </div>

        <div class="warning-box">
            <h5>⚠️ Important: WooCommerce Compatibility</h5>
            <ul>
                <li><strong>Plugin Updates:</strong> Test compatibility after WooCommerce updates</li>
                <li><strong>Theme Compatibility:</strong> Ensure custom fields display correctly with your theme</li>
                <li><strong>Performance Impact:</strong> Monitor site performance with additional fields</li>
                <li><strong>Data Migration:</strong> Plan for data migration if changing field structures</li>
                <li><strong>Third-party Plugins:</strong> Test compatibility with other WooCommerce extensions</li>
            </ul>
        </div>

        <div class="navigation-buttons">
            <a href="complete-user-guide.html" class="nav-button">← Back to Guide</a>
            <a href="payments-tutorial.html" class="nav-button">Next: Payments →</a>
        </div>
    </div>
</body>
</html>
